import React from "react";

const CollegeGuide = () => {
  return (
    <>
      <h1>College Guide</h1>
      <p>
        تامولعملاو تابساحلا ةيلك تامولعملاو ةدمتعملا تابساحلا تاعاسلا )
        سويرولاكبلا قيزاقزلا ةيلكل ةيلخادلا ةعماج ةحئلالا ماظنب قيزاقزلا ةلحرم(
        2019 ةعماج ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University
        – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 2
        نم 74 ةمئاق تايوتحملا عوضوملا مقر ةحفصلا ديهمت 4 ةحئلالا ةيلخادلا ةلحرمل
        سويرولاكبلا ةدمتعملا تاعاسلا ماظنب 5 ةيؤر ةيلكلا 5 ةلاسر ةيلكلا 5 فادهأ
        ةيلكلا 5 ماسقلأا ةيملعلا 6 تابلطتملا ةيميداكلاا حئاوللاو ةمظنملا 9 ةدام
        (1:) ةجردلا ةيملعلا 9 ةدام (2:) تابلطتم لوبقلا 9 ةدام (3:) ماظن ةساردلا
        9 ةدام (4:) ةغل سيردتلا 10 ةدام (5:) داشرلإا يميداكلأا 10 ةدام (6:)
        ليجستلا فذحلاو ةفاضلإاو 10 ةدام (7:) باحسنلاا نم ررقملا 11 ةدام (8:)
        ةبظاوملا بايغلاو 12 ةدام (9:) عاطقنلاا نع ةساردلا 12 ةدام ( 10 :) لصفلا
        نم ةيلكلا 13 ةدام ( 11 :) ماظن تاناحتملاا 13 ةدام ( 12 :) ماظن ميوقتلا
        14 ةدام ( 13 :) بوسرلا ةداعلااو 15 ( ةدام 14 تايوتسملا نيب لاقتنلاا :)
        16 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 3
        نم 74 ( ةدام 15 ةجردلا ىلع لوصحلا تابلطتم :) 16 ( ةدام 16 يناديملاو
        يلمعلا بيردتلا :) 17 ( ةدام 17 ةشقانملاو ريرقتلاو جرختلا عورشم :) 17 (
        ةدام 18 سيردت ىلع يملعلا فارشلإا :) تاررقملا 18 ( ةدام 19 ): ينورتكللإاو
        دعب نع ميلعتلا 18 ( ةدام 20 تاجردلا نايب :) 18 ( ةدام 21 عامتسلاا ماظن
        :) 19 ( ةدام 22 ةحئلالا قيبطت :) 19 ( ةدام 23 ةيساردلا تاررقملل يدوكلا
        ماظنلا دعاوق :) 19 ( ةدام 24 ةيساردلا ةحئلالا تاررقم :) 20 لوادجلا
        ةيساردلا 22 ةيساردلا تاررقملل يملعلا ىوتحملا 35 :لاوأ ةماعلا تاررقملل
        يملعلا ىوتحملا 35 :ايناث بساحلا مولع مسق تاررقمل يملعلا ىوتحملا 40
        :اثلاث تامولعم مظن مسق تاررقمل يملعلا ىوتحملا 48 :اعبار تامولعملا
        ايجولونكت مسق تاررقمل يملعلا ىوتحملا 60 :اسماخ تاررقمل يملعلا ىوتحملا
        رارقلا معدو تايلمعلا ثوحب 68 ةيساردلا ةطخلا حرتقمل جذومن 75 ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 4
        نم 74 ديهمت مت ءاشنإ ةيلك تابساحلا تامولعملاو ةعماجب قيزاقزلا بجومب رارق
        سيئر ةيروهمجلا مقر ( 84 ) ةنسل 1997 ،م ثيح مت رادصإ ةحئلالا ةيلخادلا
        ةيلكلل بجومب رارقلا يرازولا مقر ( 1209 ) خيراتب 17 /9/ 1998 .م امك مت
        ليدعت ةحئلالا ةيلخادلا ةيلكلل يتلحرم( سويرولاكبلا تاساردلاو )ايلعلا
        بجومب رارقلا يرازولا مقر ( 2645 ) خيراتب 10 / 10 / 2006 م. اريخاو مت
        ليدعت ةحئلالا ةيلخادلا ةيلكلل ةلحرم( تاساردلا )ايلعلا بجومب رارقلا
        يرازولا مقر ( 4290 ) خيراتب 17 /9/ 2018 م. ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 5
        نم 74 ةحئلالا ةيلخادلا ةلحرمل سويرولاكبلا ماظنب تاعاسلا ةدمتعملا ةيؤر
        ةيلكلا نأ نوكت ةيلك تابساحلا تامولعملاو ةعماج قيزاقزلا ةسسؤم ةدئار يف
        ميلعتلا يلاعلا ثحبلاو يملعلا يف تلااجم ةبسوحلا ةيتامولعملاو معدو رارقلا
        ىلع ىوتسملا ىلحملا يميلقلإاو .يلودلاو ةلاسر ةيلكلا مزتلت ةيلك تابساحلا
        تامولعملاو ةعماج قيزاقزلا ميدقتب همدخ ةيميلعت ةيثحبو ةزيمتم جيرختل رداوك
        تاذ تاردق هيسفانت ةيلاع نم نيصصختملا يف تلااجم ةبسوحلا ةيتامولعملاو معدو
        رارقلا مهيدل ةيعفادلا ملعتلل ىدم ةايحلا ةردقلاو ىلع ةهجاوم تابلطتم رصعلا
        تاجايتحاو قوس لمعلا ةيلاحلا ةيلبقتسملاو كلذكو ميدقت ثوحب ةيملع ةيقيبطت
        مهاست يف ةمدخ .عمتجملا فادهأ ةيلكلا فدهت ةيلك تابساحلا تامولعملاو ةعماج
        قيزاقزلا :ىلإ 1. دادعإ نيصصختملا يف مولع تابساحلا تامولعملاو تاكبشلاو
        طئاسولاو ةددعتملا ثوحبو تايلمعلا معدو رارقلا نيلهؤملاو سسلأاب ةيرظنلا
        تايجهنمو قيبطتلا امب مهنكمي نم ةسفانملا ةيملاعلا يف ريوطت تاينقت
        تابساحلا .تامولعملاو 2. ءارجإ تاساردلا ثوحبلاو ةيملعلا ةيقيبطتلاو يف
        لاجم تابساحلا تامولعملاو يتلا اهل رثأ رشابم ىلع ةيمنتلا ةلماكتملا يف
        .عمتجملا 3. ميدقت تاراشتسلاا تادعاسملاو ةيملعلا ةينفلاو تائيهلل تاهجلاو
        يتلا مدختست تاينقت تابساحلا تامولعملاو متهتو ةعانصب معدو ذاختا .رارقلا
        4. ةداعإ ليهأت بابش نيجيرخلا اقبط ةجاحل قوس لمعلا يف تلااجملا ةقلعتملا
        تابساحلاب .تامولعملاو ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig
        University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 6
        نم 74 5. قيمعت يعولا يجولونكتلا نم للاخ مادختسا تاينقت تابساحلا
        تامولعملاو يف تاعاطق تاسسؤمو ةلودلا عفرو ةءافك .اهمادختسا 6. كارتشلاا عم
        تاهجلا ةصصختملا نم لجا ريوطت بيرعتو تايجمرب مظنلا تاقيبطتلاو ةفلتخملا
        .اهب 7. ميظنت تاودنلا دقعو تارمتؤملا ةيملعلا يف لاجم مولع تابساحلا
        تامولعملاو فدهب قيمعت ميهافملا ءاقترلااو ىوتسملاب يملعلا نيب رداوكلا
        .ةصصختملا 8. دقع تايقافتلاا ةيملعلا عم تائيهلا تاسسؤملاو ةرظانملا ىلع
        ىوتسملا ىلحملا يميلقلإاو يملاعلاو فدهب لدابت تاربخلا ءارجإو ثوحبلا
        ةقلعتملا تاصصختب مولع بساحلا و ايجولونكت .تامولعملا 9. ءاشنإ تادحو ةيثحب
        ةصصختم يف عورفلا ةفلتخملا مولعل بساحلا مظنو .تامولعملا 10 . ريفوت ميعدتو
        لئاسو رشنلا ثحبلاو يملعلا يف ىتش تلااجم .صصختلا ماسقلأا ةيملعلا نوكتت
        ةيلكلا نم ماسقلأا ةيملعلا :ةـيلاتلا (1) مسق مولع بساحلا ( (Computer
        Science لخدي يف هصاصتخا سيردت ءارجإو ثوحبلا ةقلعتملا تاعوضوملاب
        تاصصختلاو ةيملعلا :ةيلاتلا تايساسأ( ميهافمو مولع بساحلا - تايساسأ تاغل
        ةجمربلا- ةجمربلا ةيلكيهلا - ةجمربلا ةيئيـشـلا - ةجمربلا ةـيـقـطـنـملا -
        ةيرـظن مـيـمـصتو تامـجرـتـملا - لـيـلـحت ميمصتو تايمزراوخلا - تاغللا
        ةيروصلا ةيرظنو تايللآا - مظن ليغشتلا - ءانب ميظنتو تابساحلا - لكايه
        تانايبلا ميظنتو تافلملا - ةجلاعملا ىلع يزاوتلا تابساحلاو ةعزوملا – قرط
        لاصتا ناسنلإا بساحلاب - ءاكذلا يعانطصلاا – ةددعتملا ءلاكولا مظن– ةينيجلا
        تايمزراوخلا- تاكبشلا ةيبصعلا - تايمزراوخلا ةيروطتلا - ةجلاعم تاغللا
        ةيعيبطلا - ةمجرتلا ةيللآا - مظن ميلعتلا بساحلاب - ةيتامولعملا ةيويحلا -
        مظن ميلعتلا ةيكذلا - بيرعت تابساحلا - ةجمرب تايقيبطتلا – ريفشتلا –
        تايجمربلا نما– تايجمربلا رابتخا– ءادلأا ةقئاف ةبسوحلا– ةنرملا ةبسوحلا–
        تابساحلا ةيرظن ). (2) مسق مظن تامولعملا ( Information Systems ) لخدي يف
        هصاصتخا سيردت ءارجإو ثوحبلا ةقلعتملا تاعوضوملاب تاصصختلاو ةيملعلا
        :ةيلاتلا ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 7
        نم 74 مظن( تامولعملا - ليلحت ميمصتو مظنلا - نيزخت عاجرتساو تامولعملا –
        مظن دعاوق تانايبلا - صلاختسا تانايبلا - دعاوق تانايبلا ةعزوملا - مظن
        تامولعملا ةيكذلا - مظن تامولعم طئاسولا ةددعتملا - مظنلا ةريبخلا - مظنلا
        ةينبملا ىلع ةفرعملا – ةسدنه ةفرعملا - مظن معد رارقلا- مظن تامولعملا
        ةيرادلإا - ةسدنه تايجمربلا - مظن ةنكيم لمعلا يبتكملا - مظن ةنكيم
        تابتكملا - مظن تامولعم ةرادإ تامزلأا - تايداصتقا مظن تامولعملا – مظن
        تامولعم تاسسؤملا ةيضارتفلاا و تاكرشلا ةيمقرلا - ةراجتلا ةينورتكللإا -
        مظن تامولعم تنرتنلإا - مظن تامولعملا ةيجيتارتسلإا - ةرادإ زكارم
        تامولعملا – تاعدوتسم تانايبلا – تانايبلا يف بيقنتلا – تايجهنم ريوطت مظن
        تامولعملا – ةدوج ديكأت تامولعملا مظنو تايجمربلا - مظن نامأ تامولعملا -
        ةسدنه مظنلا – لا ويج ةيتامولعم- مظن تامولعملا ةيفارغجلا - راعشتسلاا نع
        دعب – ليلحت روصلا ةيوجلا – مظن ديدحت عقاوملا ةيضرلأا مادختساب رامقلأا
        ةيعانصلا - دعاوق تانايبلا ةيفارغجلا - ايفارجوتراكلا ةيمقرلا ديسجتلاو
        يئرملا - تاينقت عمج تانايبلا ةيفارغجلا - ليلحتلا ةجذمنلاو ةيفارغجلا -
        ةجمرب مظن تامولعملا ةيفارغجلا راعشتسلااو نع دعب - مظن تامولعملا
        ةيفارغجلا ةلومحملا - طيطخت ةرادإو مظن تامولعملا ةيفارغجلا– تانايبلا
        مولع- ليلحت تانايبلا – تانايبلا ةمخضلا – يف بيقنتلاو ليلحتلا يعامتجلاا
        لصاوتلا تاكبش- تامولعملا مظن قيقدتو ةعجارم– لتكلا ةلسلس ةينقت- مظن
        تاـقـيـبـطت ... ةيفارغجلا ،ةيركسعلا ،ةيعارزلا ،ةيبطلا ،ةيبساحملا
        ،ةيرادلإا :تلااجملا ةفاك يف تامولـعملا )خلا . (3) مسق ايجولونكت
        تامولعملا ( Information Technology ) لخدي يف هصاصتخا سيردت ءارجإو ثوحبلا
        ةقلعتملا تاعوضوملاب تاصصختلاو ةيملعلا :ةيلاتلا تاكبش( بساحلا اهتارادإو -
        نما تاكبشلا - ةجمرب تاكبشلا - لسارت تانايبلا – ايجولونكت تلااصتلاا -
        ةجلاعم تاراشلإا ةيمقرلا ةيئوضلاو - فرعتلا ىلع ملاكلا هديلوتو - ةجلاعم
        روصلا - مظن مسرلا بساحلاب - موسرلا ةكرحتملا - عقاولا يضارتفلاا - طئاسولا
        ةددعتملا - ايجولونكت تنرتنلإا اهتجمربو - طغض تانايبلا – تايرامعم بساحلا
        – مظنلا ةيمقرلا – ءايزيف تاينورتكللإا - تاجلاعملا ةقيقدلا اهتاقيبطتو –
        مظنلا ةجمدملا – ناسنلإا يللآا ةيؤرلاو بساحلاب – ةبسوحلا ةيباحسلا – مكلا
        ةبسوح – تنرتنا ءايشلأا– يناربيسلا نملاا– )ةلقنتملا ةبسوحلا . (4) مسق و
        تايلمعلا ثوحب معد رارقلا ( (Operations Research and Decision Support
        لخدي يف هصاصتخا سيردت ءارجإو ثوحبلا ةقلعتملا تاررقملاب تاصصختلاو
        :ةيلاتلا ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 8
        نم 74 تايساسأ( ميهافمو تايرظنو ذاختا رارقلا- ثوحب تاـيـلـمـعلا
        ةـيـجـهنمو مـعد رارـقـلا- ةـجمربلا ةيطخلا ريغو ةيطخلا - ةجمربلا
        ةيئاوشعـلا ةيكيمانيدلاو - ةيرظن تاكبشلا طيـطختو تاعورـشمـلا - مظن فوفص
        راظتنلاا- ةجمربلا ةددعتم فادهلأا ريياعملاو- تاودأ بيلاسأو مـعد رارـقـلا
        - مظن ةبقارم نوزخملا جاتنلإاو - ةرادإ تاعورشملا - ةرادإ تامزلأا
        رطاخملاو- تاقيبطت ثوحب تايلمعلا معدو رارقلا – تاقيبطت تاباسحلا ةيكذلا يف
        ثوحب تايلمعلا معدو رارقلا – تايساسأ ميهافمو ملع ةرادلإا – تانايبلا مولع-
        تانايبلا ليلحت– ةرادإ تايتسجوللا دادملاا لسلاسو – جذامنلا ةيمكلا يف ملع
        ةرادلإا داصتقلااو – داصتقلاا يسدنهلا - ةرادلإا ةيجيتارتسلاا - ةيرظن
        تايرابملا - ةجذمنلا ةاكاحملاو – ةرادإ دراوملا ةيرشبلا كولسلاو يميظنتلا –
        مولع ةيلبقتسملا تاساردلا– ؤبنتلا تايجهنم - جذامنلا ةيقيبطتلا ثوحبل
        تايلمعلا يف ةفاك تلااجملا ،ةيداصتقلاا ،ةيسايسلا ،ةيعامتجلاا ،ةيركسعلا
        ... .)خلا زوجيو نأ أشنت ةيلكلاب ماسقأ ىرخأ اقفو نوـــناقل مـــيظنت
        تاــــعماجلا ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig
        University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 9
        نم 74 تابلطتملا ةيميداكلاا حئاوللاو ةمظنملا ةدام (1:) ةجردلا ةيملعلا
        حنمت ةعماج قيزاقزلا ءانب ىلع بلط سلجم ةيلكلا ةجرد سويرولاكب تابساحلا
        تامولعملاو يف ىدحإ تاصصختلا :ةيلاتلا 1- مولع بساحلا . Computer Science.
        2- مظن تامولعملا . Information Systems. 3- لا ويج ةيتامولعم .
        Geoinformatics. 4- ايجولونكت تامولعملا . Information Technology. 5-
        رارقلا معدو تايلمعلا ثوحب . Operations Research and Decision Support
        ةدام (2:) دعاوق لوبقلا − متي لوبق بلاطلا ةساردلل ةيلكلاب ءانب ىلع
        دعاوقلا يتلا اهددحي بتكم قيسنت لوبقلا تاعماجلاب لك ماع نم نيب بلاطلا
        نيلصاحلا ىلع ةيوناثلا ةماعلا وأ ام اهلداعي ىلع نأ نوكي دق سرد ررقم
        ءايزيفلا ررقمو ةضاير (2.) − متي لوبق بلاط تاداهشلا ةلداعملا نيدفاولاو
        بسح دعاوقلا ةمظنملا كلذل يتلاو اهعضت تاهجلا ةصتخملا − زوجي نأ ىفعي
        بلاطلا لوحملا ةيلكلل نم ةيلك ةيعماج وأ دهعم يملع رظانم فرتعم هب نم ءادأ
        ناحتملاا يف ضعب تاررقملا ةيساردلا دعب ءارجإ ةصاقم ةفرعمب ماسقلأا ةصتخملا
        اهدامتعاو نم سلجم ةيلكلا اذإ تبث هنا ىدأ حاجنب تاناحتما اهلداعت يف
        ةيلكلا وأ دهعملا لوقنملا هنم . ةدام (3:) ماظن ةساردلا أ- دمتعت ةساردلا
        ةيلكلاب ىلع ماظن تاعاسلا ،ةدمتعملا مسقيو ماعلا يساردلا ىلا نيلصف نيسارد
        ،نييماظن نوكتو ةعاسلا ةدمتعملا يه ةدحو سايق ةيسارد ديدحتل نزو ررقملا
        يساردلا . ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 10
        نم 74 ب- زوجي ل سلجم ةيلكلا ةقفاوملا ىلع دقع لوصف ةيفيص ةفثكم يف ضعب
        تاررقملا ءانب ىلع حارتقا ةنجل نوئش ميلعتلا بلاطلاو اقفوو امل حمست هب
        تايناكمإ فورظو .ةيلكلا ج- دلا ر ةسا يف ىوتسملا لولأا يناثلاو ةماع عيمجل
        تاصصختلا ةيلكلاب أدبيو صصختلا يف ىوتسملا ثلاثلا دنع تجا يزا بلاطلا رثكأ
        نم 60 .ةعاس لكلو مسق نأ عضي طورشلا ةلهؤملا قاحتللال هب دعب قإ ر اهرا نم
        سلجم .ةيلكلا د- بلطتي لوصحلا ىلع سويرولاكبلا نا زاتجي بلاطلا حاجنب 138
        ةعاس ةدمتعم كلذو ىلع ىدم ةينامث لوصف ةيسارد ىلع ،لقلاا ةمسقم ىلا ةعبرا
        تايوتسم .ةيسارد ( ةدام 4 سيردتلا ةغل :) ةساردلا يف ةيلكلا ةغللاب ةغللاب
        داسفلا ةحفاكمو ناسنلاا قوقح ررقم ادع( ةيزيلجنلإا )ةيبرعلا . ةدام (5):
        داشرلإا يميداكلأا ددحت ةيلكلا كل ل ةعومجم نم بلاطلا ادشرم ايميداكأ نم
        ءاضعأ ةئيه ،سيردتلا موقي ماهمب داشرلإا ا يميداكلأ بلاطلل هتدعاسمو ىلع
        رايتخا تاررقملا يتلا اهسردي ليجستلاو اهيف ههيجوتو لاوط ةرتف اهتسارد
        ،ةيلكلاب ربتعيو ىأر دشرملا يميداكلأا ايراشتسا بلاطلاو وه لوئسملا نع
        تاررقملا يتلا موقي ليجستلاب اهيف ءانب ىلع هتبغر طرشب نا نوكي بلاطلا دق
        زاتجا حاجنب بلطتم ليجستلا اذهل ررقملا . ةدام (6): ليجستلا فذحلاو
        ةفاضلإاو أ- عم ةيادب لك لصف يسارد موقي بلاطلا ليجستب تاررقملا ةيساردلا
        يتلا ،اهراتخي كلذو يف تاقولاا يتلا اهددحت ةرادإ ةيلكلا لبق ءدب ماظتنا
        .ةساردلا ب- ددحي سلجم ةيلكلا دحلا ىندلأا ددعل بلاطلا ليجستلل يف لك ررقم
        ءانب ىلع حارتقا ةنجل نوئش ميلعتلا بلاطلاو . ج- ددع تاعاس ليجستلا ةبسنلاب
        لوصفلل :ةيماظنلا ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig
        University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 11
        نم 74 • دحلا ىندلأا تاعاسلل ةدمتعملا ليجستلل (9) ،تاعاس زوجيو زواجتلا نع
        لاحد ىندلاا اذإ ناك ددع تاعاسلا ةيقبتملا بلاطلل جرختلل لقا نم 9 وا يف
        ةلاح مدع حرط تاررقم ةيقاب بلاطلل اهتساردل يف تاذ لصفلا .طقف • دحلا
        ىصقلاا تاعاسلل ةلجسملا بلاطلل 18 ةعاس ةدمتعم . • دحلا ىصقلاا تاعاسلل
        ةلجسملا بلاطلل نيبقارملا ايملع ( ملا ت نيرثع ايسارد و نيلصاحلا ىلع طسوتم
        طاقن ةيمكارت CGPA يف ةيادب لصفلا يساردلا لقأ نم 2) وه 12 ةعاس .ةدمتعم •
        نكمي ةدايز دحلا ىصقلاا تاعاسلل ةلجسملا بلاطلل نيلصاحلا ىلع طسوتم طاقن
        ةيمكارت CGPA يف ةيادب لصفلا يساردلا ىلعأ نم وا ىواسي 2 ىلا 21 ةعاس
        ةدمتعم كلذو يعاودل جرخت .بلاطلا ةبسنلاب لصفلل :يفيصلا • دحلا ىصقلاا
        تاعاسلل ةلجسملا بلاطلل وه 6 تاعاس .ةدمتعم • نكمي ةدايز دحلا ىصقلاا
        تاعاسلل ةلجسملا بلاطلل ىلا 9 تاعاس ةدمتعم كلذو يعاودل جرخت بلاطلا . د-
        زوجي بلاطلل دعب لامكإ تاءارجإ ليجستلا نا فذحي وا فيضي اررقم وا رثكأ كلذو
        للاخ ةرتف اهددحت ةيلكلا فذحلل ،ةفاضلإاو متيو كلذ قيسنتلاب عم دشرملا
        يميداكلأا بلاطلل . ه- حمسي بلاطلل ةساردب تاررقملا ةفلتخملا ليجستلاو يف
        تاررقم تايوتسملا ىلعلاا ءانب ىلع همايق رايتخاب تاررقملا ةبولطملا
        تابلطتمك تاررقملل ىلعلاا يف لصف يسارد قباس ، لاو متي ليجست بلاطلا يف
        ررقم ىلعا لاا اذإ حجن يف .هتابلطتم ةدام (7): باحسنلاا نم ررقملا أ- زوجي
        بلاطلل دعب ليجست تاررقملا يتلا اهراتخا نا بحسني نم ررقم وا رثكأ للاخ
        ةرتف ةددحم ي اهنلع سلجم ةيلكلا ثيحب لا لقي ددع تاعاسلا ةلجسملا بلاطلل نع
        دحلا ىندلاا ليجستلل يف لصفلا يساردلا دحاولا ىفو هذه ةلاحلا لا دعي بلاطلا
        ابسار يف تاررقملا يتلا بحسنا اهنم بسحيو هل ريدقت "بحسنم" يميداكلأا هلجس
        نمض لجستو . ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University
        – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 12
        نم 74 ب- اذإ بحسنا بلاطلا نم ررقم وا رثكأ دعب ةرتفلا ةددحملا كلذل نود
        رذع يرهق هلبقي سلجم ةيلكلا بستحي هل ريدقت "بسار" يف تاررقملا يتلا بحسنا
        .اهنم ةدام (8): ةبظاوملا بايغلاو أ- ةساردلا يف ةيلكلا ةيماظن عضختو ةيلمع
        ةعباتم روضح بلاطلا طورشل حئاولو اهددحت ةرادا .ةيلكلا ب- بلطتي لوخد
        بلاطلا ناحتملاا يئاهنلا قيقحت ةبسن روضح لا لقت نع 75% نم تارضاحملا
        نيرامتلاو لخاد مرحلا يعماجلا يف لك ،ررقم اذإو تزواجت ةبسن بايغ بلاطلا –
        نود رذع لوبقم – يف دحأ تاررقملا 25% نوكي ل سلجم ةيلكلا هنامرح نم لوخد
        ناحتملاا يئاهنلا دعب هراذنا اقفو دعاوقلل ةمظنملا كلذل ، ربتعيو بسار
        لجسيو نامرح . اما اذإ مدقت بلاطلا رذعب هلبقي سلجم ةيلكلا ىفو( ةرتفلا
        )ةددحملا بستحي هل ريدقت "بحسنم" يف ررقملا يذلا مدق هنع رذعلا . ج- بلاطلا
        يذلا غي يب نع ناحتملاا يئاهنلا يلأ ررقم – نود رذع لوبقم – ربتعي بسار
        لجسيو بايغ يف كلذ ررقملا ، نيعتيو هيلع ةداعا ةسارد ررقملا ةرم .ىرخا د-
        اذإ مدقت بلاطلا رذعب يرهق دعب هضرع ىلع سلجم ةيلكلا نع مدع روضح ناحتملاا
        يئاهنلا يلأ ررقم للاخ نيموي نم ءارجإ ناحتملاا بستحي هل ريدقت ريغ" "لمتكم
        يف اذه ررقملا طرشب نأ نوكي لاصاح ىلع 60% ىلع لقلأا نم تاجرد لامعلأا
        ،ةيلصفلا لاأو نوكي دق مت هنامرح نم لوخد تاناحتملاا .ةيئاهنلا ىفو هذه
        ةلاحلا حاتي بلاطلل لصاحلا ىلع ريدقت ريغ" "لمتكم ةصرف ءادأ ناحتملاا
        يئاهنلا يف لوأ ةرم متي ضرع اذه ررقملا ،ليجستلل لاإو ربتعا ابسار يف
        ررقملا . بستحتو ةجردلا ةيئاهنلا بلاطلل ىلع ساسأ ةجردلا لصاحلا اهيلع يف
        ناحتملاا يئاهنلا ةفاضإ ىلإ ةجردلا قباسلا لوصحلا اهيلع يف لامعلأا ةيلصفلا
        . ةدام (9): عاطقنلاا نع ةساردلا أ- ربتعي بلاطلا اعطقنم نع ةساردلا اذإ
        بيغت نع روضحلا يف عيمج تاررقم لصفلا يساردلا نودب رذع لوبقم وأ مل لجسي
        تاررقملا يف لصف يسارد للاخ ديعاوم ليجستلا ةررقملا . ب- زوجي بلاطلل نا
        مدقتي بلطب فاقيلإ ديقلا ةيلكلاب بسح طورشلا طباوضلاو يتلا اهعضت .ةعماجلا
        ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty
        of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 13
        نم 74 ةدام ( 10 ): لصفلا نم ةيلكلا أ- اذإ ضفخنا لدعملا يمكارتلا بلاطلل
        ىلا لقا نم 2 يف يأ لصف يسارد يسيئر هجوي هل راذنا ،يميداكأ ىضقي ةرورضب
        عفر بلاطلا هلدعمل يمكارتلا ىلا 2 ىلع .لقلاا ب- لصفي بلاطلا رذنملا
        ايميداكأ نم ةساردلا كلاب لةي يف تلااحلا :ةيلاتلا • اذإ رركت ضافخنا هلدعم
        يمكارتلا نع 2 يف ةعبرا لوصف ةيسارد ةيسيئر .ةعباتتم • اذإ عطقنا نع
        ةساردلا ةدمل لوطا نم نيلصف نييسارد نييماظن نييلاتتم وا ثلاث لوصف ةيسارد
        ةيماظن ريغ ةيلاتتم نود رذع هلبقي سلجم .ةيلكلا ج- زوجي ل سلجم ةيلكلا نا
        رظني يف ةيناكما حنم بلاطلا ضرعملا لصفلل ةجيتن مدع هنكمت نم عفر هلدعم
        يمكارتلا ىلا 2 هصرف هدحاو هريخاو اهتدم نييلصف نييماظن نيلاتتم عفرل هلدعم
        ىلا 2 قيقحتو تابلطتم جرختلا اذإ ناك دق متا حاجنب ةسارد 70% ىلع لقلاا نم
        تاعاسلا ةدمتعملا ةبولطملا جرختلل . ةدام ( 11 ): ماظن تاناحتملاا أ-
        ةجردلا ىمظعلا لكل مق رر يه 100 .ةجرد ب- دحلا ىندلاا نلل جاح يف ررقملا
        يساردلا وه 50% نم عومجم تاجرد ،ررقملا و 30% ىلع لقلاا نم تاجرد ناحتملاا
        يريرحتلا . ج- عيزوت تاجرد ناحتملاا يف لك ررقم ىلع وحنلا :يلاتلا • 60%
        ناحتملال يريرحتلا ةياهن لصفلا يساردلا . • 40% متي عيزوت ها لمشتل لامعلأا
        ةيلصفلا ىلع وحنلا :يلاتلا − 15% تاناحتملال يتلا اهيرجي ذاتسلأا ةفصب
        ةيرود تاناحتملااو ةيلمعلا وأ لامعلأا يتلا فلكي اهب بلاطلا ءانثأ لصفلا
        يساردلا − 15 % ناحتملا فصتنم لصفلا يساردلا − 10% تاناحتما ةيوفش ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 14
        نم 74 د- نوكي ل سلجم ةيلكلا ديدحت ديعاوم تاناحتما فصتنم لصفلا ،يساردلا
        تاناحتملاا ةيوفشلا ةيلمعلاو ، تاناحتملااو ةيئاهنلا اذكو ناحتملاا ةقيرط
        اقبط ةعيبطل ررقملا اهنلاعاو بلاطلل يف تقو بسانم . ه- ناحتملاا يئاهنلا
        اناحتما ايريرحت يف عيمج تاررقملا زوجيو سلجمل ةيلكلا ءانبو ىلع حارتقا
        ماسقلأا ،ةصتخملا ةقفاوملا ىلع دقع ناحتملاا يئاهنلا ماظنب باتكلا حوتفملا
        ( Open Book ) وأ ناحتملاا ينورتكللإا ( Computer- Based Exam ) نوكتو
        ناحتملاا تاعاس ددع ررقملل ةدمتعملا تاعاسلا ددعل ةيواسم . ةدام ( 12 ):
        ماظن ميوقتلا أ- نوكي ماظن مييقتلا ىلع ساسا ريدقتلا يف لك ررقم يسارد
        ماظنب طاقنلا يذلاو ددحي اقبط لودجلل يلاتلا : ةبسنلا ةيوئملا (%) زمرلا
        طاقنلا ريدقتلا نم 90% ىلإ 100 % +A 4.0 زاتمم نم 85% ىلإ لقأ نم 90% A 3.7
        نم 80% ىلإ لقأ نم 85% B+ 3.3 ديج ادج نم 75% ىلإ لقأ نم 80% B 3 نم 70%
        ىلإ لقأ نم 75% C+ 2.7 ديج نم 65% ىلإ لقأ نم 70% C 2.4 نم 60% ىلإ لقأ نم
        65% D+ 2.2 لوبقم نم 50% ىلإ لقأ نم 60% D 2 لقأ نم 50% F رفص بسار ب- بسحت
        نازوأ تاريدقت تاررقملا ىلع وحنلا :يلاتلا • متي باسح عومجم طاقن ررقملا
        ىلع ساسأ لصاح برض ددع طاقنلا يتلا لصحي اهيلع بلاطلا يف ددع تاعاسلا
        ةدمتعملا ررقملل كلذو برقلأ نيمقر .نييرشع ةعماج قيزاقزلا– لك ةي تابساحلا
        تامولعملاو Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 15
        نم 74 • لدعملا يلصفلا ( GPA ) وه طسوتم ام لصحي هيلع بلاطلا نم عومجم طاقن
        يف لصفلا ،يساردلا بسحيو ىلع ساسأ لصاح ةمسق عومجم طاقنلا تاررقملل ةلجسملا
        يف لصفلا يساردلا ىلع يلامجإ ددع تاعاسلا ةدمتعملا تاررقملل ةلجسملا يف سفن
        لصفلا كلذو برقلأ نيمقر .نييرشع ج- متي باسح ريدقت / لدعملا يمكارتلا امك
        :يلي • متي باسح طسوتم ام لصحي هيلع بلاطلا نم عومجم طاقن تاررقملا يتلا
        ،اهسرد متيو هباسح ىلع ساسأ عومجم طاقن عيمج تاررقملا ةلجسملا للاخ تارتفلا
        ةيساردلا ةقباسلا باسحل لدعملا اموسقم ىلع عومجم ددع تاعاسلا ةدمتعملا
        ةلجسملا للاخ تارتفلا ةقباسلا كلذو برقلأ نيمقر .نييرشع لدعملا يمكارتلا (
        CGPA ) = عومجم طاقنلا / يلامجأ تاعاسلا .ةلجسملا د- متي باسح ريدقتلا
        ماعلا جرختلل اقبط لودجلل :يلاتلا ريدقتلا ماعلا لدعملا يمكارتلا زمرلا
        ريدقتلا ةبسنلا ةيوئملا طاقنلا A زاتمم 85 رثكأف 3.7 رثكأف B ديج ادج 75 –
        لقأ نم 85 3 – لقأ نم 3.7 C ديج 65 – لقأ نم 75 2.4 – لقأ نم 3 D لوبقم 50
        – لقأ نم 65 2 – لقأ نم 2.4 F فيعض لقأ نم 50 لقأ نم 2 ه- حنمت ةبترم فرشلا
        بلاطلل دنع هجرخت طرشب لاأ لقي طسوتم طاقن يأ ىوتسم يسارد نع ( 3.00 ) لاأو
        ديزت ةرتف ةساردلا نع عبرأ تاونس لااو نوكي بلاطلا دق بسر يف يأ ررقم للاخ
        تاونس .ةساردلا ةدام ( 13 ): بوسرلا ةداعلااو أ- حمسي بلاطلل يذلا بسري يف
        دحأ تاررقملا نا ديعي ليجست كلذ ررقملا ةسارد( ) اناحتماو بستحي هل ريدقتلا
        دحب ىصقأ ( D+ ) ةبسنبو 64% دحك ىصقا ىلعأ( ةجرد )لوبقملل اذإو ناك ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 16
        نم 74 ررقم يرايتخا هيلعف ةداعإ ةسارد كلذ ررقملا وأ رايتخا ررقم رخا ليدب
        يفو هذه ةلاحلا ظفتحي بلاطلا ةجردلاب ريدقتلاو لصاحلا هيلع ما يف ررقملا
        يرايتخلاا .ديدجلا ب- بلاطلا يذلا بسري يف ررقم رثكأ نم ةرم ىفتكي باستحاب
        ررقملا ةرم هدحاو يف هلدعم يمكارتلا امهم تددعت تارم بوسرلا لجستو تارم
        بوسرلا يف هلجس .يميداكلأا ( ةدام 14 نيب لاقتنلاا :) تايوتسملا ف بلاطلا
        ىوتسم ددحتي ي :يلاتلاك يساردلا ماعلا ةيادب أ- زاتجي مل املاط لولأا
        ىوتسملاب ديقم بلاطلا لظيو ةيلكلاب هقاحتلا دنع لولأا ىوتسملاب بلاطلا ديقي
        28 .ةدمتعم دعاس ب- هزايتجا دنع يناثلا ىوتسملل لولاا ىوتسملا نم بلاطلا
        لقتني 28 .ةدمتعم ةعاس ج- هزايتجا دنع ثلاثلا ىوتسملل يناثلا ىوتسملا نم
        بلاطلا لقتني 60 .ةدمتعم ةعاس د- هزايتجا دنع عبارلا ىوتسملل ثلاثلا
        ىوتسملا نم بلاطلا لقتني 100 .ةدمتعم ةعاس ةدام ( 15 ): تابلطتم لوصحلا ىلع
        ةجردلا لوصحلل ىلع ةجرد سويرولاكبلا بجي نا زاتجي بلاطلا 138 ةعاس ةدمتعم
        ىلع لقلاا نم تاررقملا كلذكو بيردتلا يلمعلا يناديملاو بسح جمانربلا
        عوضوملا لدعمبو يمكارت لا لقي نع 2.00 .)لوبقم( :لاوأ تابلطتملا ةماعلا
        )ةعماجلا تابلطتم( ( 12 ) ةعاس ةدمتعم أ- تاررقملا ةيرابجلاا (6 تاعاس
        ةدمتعم )ةيرابجا ب- تاررقملا ةيرايتخلاا (6 تاعاس )ةدمتعم :ايناث تابلطتم
        ةيلكلا ( 66 ) ةعاس ةدمتعم أ- مولع ةيساسأ ( 24 ةعاس ةدمتعم )ةيرابجا ب-
        مولع بساح ةيساسأ ( 42 ةعاس ةدمتعم )ةيرابجا :اثلاث تابلطتم صصختلا ( 60 )
        ةعاس ةدمتعم ج- تاررقملا ةيرابجلاا ( 42 تاعاس ةدمتعم )ةيرابجا د- تاررقملا
        ةيرايتخلاا ( 12 تاعاس )ةدمتعم ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو
        Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 17
        نم 74 ه- عورشم جرختلا (6 تاعاس )ةدمتعم :اعبار بيردتلا يلمعلا يناديملاو
        داسفلا ةحفاكمو ناسنلاا قوقح ررقم :اسماخ ( لدعملا وا يلكلا عومجملا يف
        بسحت لاو بوسرو حاجن ةدام بلاطلل يمكارتلا سرديو ) .نأشلا اذه يف ةعماجلا
        سلجم اهرقي يتلا دعاوقلل اقبط ةدام ( 16 ): بيردتلا يلمعلا يناديملاو أ-
        بيردتلا يلمعلا يناديملاو : − بجي ىلع بلاطلا روضح بيردتلا يلمعلا
        يناديملاو ةدمل رهش لبق جرختلا للاخ يا ةلطع ةيفيص دعب تجا ي هزا 60 ةعاس
        ةدمتعم . − كلذو تحت فارشإ ءاضعأ ةئيه سيردتلا ةئيهلاو ةنواعملا كلذو
        ةعباتمل نيكراشملا يف بيردتلا عضوو مييقتلا صاخلا لكب مهنم اقبط ريياعملل
        يتلا متي اهديدحت نم لبق ماسقلأا سلاجم اهدمتعيو ةصتخملا سلجم ةيلكلا . −
        مزتلي بلاطلا ةباتكب ريرقت نع ةرتف هبيردت هميلستو وضعل ةئيه سيردتلا
        لوؤسملا .هنع − بجي ءانثا ةيلكلاب يفيصلا مرتلا للاخ تاررقم يأ ليجست مدع
        روضح يلمعلا بيردتلا يناديملاو . ب- طرتشي حاجن بلاطلا يف بيردتلا يلمعلا
        يناديملاو لوصحلل ىلع ةجرد .سويرولاكبلا ةدام ( 17 :) عورشم جرختلا
        ريرقتلاو ةشقانملاو أ- موقي بلاط ىوتسملا عبارلا دادعإب عورشم سويرولاكب يف
        تاعوضوم ةقلعتم مهصصختب ت اهددح جم اسل ماسقلأا ةصتخملا كلذو للاخ ماعلا
        يساردلا .هلك زوجيو صيصخت ةرتف ةيفاضإ عورشملل أدبت بقع ءاهتنلاا نم ناحتما
        لصفلا يساردلا يناثلا ةدملو عبرأ عيباسأ ىلع رثكلأا نوكتو تحت فارشإ ءاضعأ
        ةئيه سيردتلا ميظنتل دادعإ عيراشملا اهجارخإو يف اهتروص ةيئاهنلا
        .اهتشقانمل ب- مدقي بلاطلا اريرقت ايملع نع عوضوم عورشم جرختلا يف ةياهن
        ةرتفلا ةصصخملا .عورشملل ج- لكشي سلجم ةيلكلا ةنجل ةشقانم مييقتو ريراقتلا
        ةصاخلا تاعورشملاب ةمدقملا نم .بلاطلا د- ردقي عورشملا ةميقب (6) تاعاس
        ةدمتعم دتميو نيلصفل .نييسارد ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو
        Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 18
        نم 74 ةدام ( 18 :) فارشلإا يملعلا ىلع سيردت تاررقملا أ- هررقي امل اقبط
        ةيصصختلا تاررقملا سيردتب اهيلإ راشملا ةيملعلا ماسقلأا نم مسق لك صتخي
        ةيلكلا سلجم و فرشي ةيلكلا ديمع ىلع ةيلكلاب ةيملع ماسقأ اهل سيل يتلا
        تاررقملا . ب- موقي لك مسق دادعإب فيصوت لماك تايوتحمل تاررقملا يتلا موقي
        ،اهسيردتب ضرعتو هذه تايوتحملا ىلع سلاجم ماسقلأا ةصتخملا دعبو اهدامتعا نم
        سلجم ةيلكلا حبصت هذه تايوتحملا ةمزلم ءاضعلأ ةئيه سيردتلا نيمئاقلا سيردتب
        كلت .تاررقملا ج- زوجي سلجمل ةيلكلا ءانب ىلع حارتقا سلاجم ماسقلأا ا
        ةصتخمل ليدعت تابلطتم ليجستلا يوتحملاو يملعلا امب لا ديزي نع 25% نم
        ىوتحملا يلأ ررقم نم تاررقملا .ةيساردلا د- زوجي ل سلجم ةيلكلا سلجمو
        ةعماجلا و ماسقلأا سلاجم حارتقا ىلع ءانب ا ةصتخمل ةفاضا تاررقم مئاوقل
        تاررقملا ةيرايتخلاا ةبكاومل روطتلا عيرسلا يف صصختلا ةيناكماو ةباجتسلاا
        ىلا ريغتلا يف تابلطتم قوس لمعلا ططخو .ةيمنتلا ه- ي موق سلجم ةيلكلا وجم
        اسل ماسقلأا ةصتخملا ةعباتمب بلاطلا ايرود نم للاخ قيسنتلا عم دشرملا
        ،يميداكلأا يطعيو لك بلاط انايب هتلاحب ةيساردلا اذإ رهظ يندت هاوتسم عضيو
        طباوضلا يتلا نكمي نم اهللاخ ةعباتم نيسحتو ةلاح .بلاطلا و- سلجمل ةيلكلا
        نأ مظني تارود ةيبيردت وأ تاسارد ةيطيشنت بلاطلل يف تاعوضوملا يتلا لخدت
        نمض تاصصخت ةيلكلا ءانب ىلع حارتقا سلاجم ماسقلأا ةصتخملا . ( ةدام 19 ):
        ينورتكللإاو دعب نع ميلعتلا يساردلا ماظنلا حمسي ةيلكلاب ويديفلا وا
        تنرتنلاا قيرط نع جمدملا ميلعتلا ةقيرطب تاررقملا ضعب سيردتب ةقفاوم دعب
        كلذو دعب نع ميلعتلا لئاسو نم ةليسو يأ وا سنارفنوك ةصتخملا ماسقلأا سلاجم
        ةيلكلا سلجمو هسيردت متي ام ةبسن ديزت لاأ ىلع دعب نع ميلعتلا بولسا
        مادختساب نع 25 عيمج ىفو ،ررقملا نم % يعماجلا مرحلا لخاد يلمعلا بيردتلاو
        يئاهنلا ناحتملاا ىرجي لاوحلأا ( ةدام 20 تاجردلا نايب :) أ- .يميداكلأا
        هلجس تاجردب نايب ىلع لصحي نا بلاطلل قحي ةعماج قيزاقزلا– لك ةي تابساحلا
        تامولعملاو Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 19
        نم 74 ب- نم بحسنا يذلا وأ سويرولاكبلا ةجرد ىلع لصحو هتسارد ىهنأ يذلا
        بلاطلل قحي ةيلكلا نا .يميداكلأا هلجس تاجردب نايب ىلع لصحي ج- وأ ةريشأتلا
        فورظل هيلا جاتحا ىتم يميداكلأا هلجس تاجردب نايب دفاولا بلاطلا ءاطعإ زوجي
        .ةماقلإا د- لاي ديدست مدع ةلاح يف بلاطلا تاجردب نايب ىطع .ةيساردلا
        موسرلا ه ةدام ( 21 ): ماظن عامتسلاا زوجي سلجمل ةيلكلا دعب ذخا يأر سلاجم
        ماسقلأا ةصتخملا نا لبقي بلاط نم تايلك وا تاعماج ىرخأ نم رصم وا جراخلا
        نيعمتسمك ضعبل تاررقملا ةيلكلاب اقفو طورشل دعاوقو اهددحي سلجم ةيلكلا
        حنمتو ةيلكلا ةدافا هذهب تاررقملا انيبم اهيف ريدقت بلاطلا لاو عبتي كلذ يأ
        ةجرد ةيعماج ةيساردلا موسرلا دادس دعب كلذو ةيلكلا سلجم اهددحي يتلا . ةدام
        ( 22 ): قيبطت ةحئلالا أ- قبطت هذه ةحئلالا نم خيرات رودص رارقلا يرازولا
        هدامتعاب ا ىلع بلاـطلا ،نيدجتـسملا اـمأ بلاـطلا نيديقملا لبق اذه خيراتلا
        ىرستف مهيلع ةحئلالا ةيلخادلا ماعل 2006 دعاوقلاو ةعبتملا ةلمكملا اهل . ب-
        قبطي اميف مل دري هنأشب صن يف هذه ةحئلالا ماكحأ نوناق ميظنت تاعماجلا مقر
        ( 49 ) ةنسل 1972 هتحئلاو ةيذيفنتلا نيناوقلاو ةلدعملا امهل كلذكو تارارقلا
        ةيرازولا ةينبملا ىلع تارارق ةرداص نم سلجملا ىلعلأا .تاعماجلل ( ةدام 23
        ةيساردلا تاررقملل يدوكلا ماظنلا دعاوق :) أ- وا مسقلل يدوكلا زمرلا نم
        ررقم يأ دوك نوكتي جمانربلا ماقرأ ةثلاث نم نوكم ددع كلذ يليو :يتلآاك
        اهليصفت • مقرلا تائملا ةناخ يف يساردلا ىوتسملا لثمي • مقرلا يف ناخ يت
        داحلآا و تاررقم زييمتل مدختسي تارشعلا جمانربلا وا مسقلا سردت يتلاو
        يساردلا ىوتسملا سفنل ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig
        University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 20
        نم 74 ب- ةيملعلا ماسقلأل يزمرلا ماظنلا / جماربلا لسلسم مسا / مسقلا
        جمانربلا زمرلا 1 بساحلا مولع Computer Sciences CS 2 تامولعملا مظن
        Information Systems IS 3 لا ويج ةيتامولعم Geoinformatics GI 4 تامولعملا
        ايجولونكت Information Technology IT 5 رارقلا معد Decision Support DS ج-
        ةيساسلأا مولعلا ،ةيناسنلإا مولعلا تاررقمل يزمرلا ماظنلا . مسا ررقملا
        زمرلا 1 ةيناسنلاا مولعلا Humanities HU 2 ةيساسلاا مولعلا Basic Science
        BS د- ةيساردلا تايوتسملا داوكأ يساردلا ىوتسملا (Academic Level) دوكلا
        (Level Code) لولأا First 1 يناثلا Second 2 ثلاثلا Third 3 عبارلا Fourth
        4 ةدام ( 24 ): تاررقم ةحئلالا ةيساردلا لودجلا يلاتلا صخلي تايعونلا
        ةفلتخملا تاررقمل ةحئلالا ةيساردلا حضومو هب يلامجأ ددع تاعاسلا ةدمتعملا
        لكل ةيعون ةبسنلاو ةيوئملا اهل ةنراقم ةبسنلاب ةرظانملا اهل يف National
        Academic Reference Standards (NARS) ةعماج قيزاقزلا– لك ةي تابساحلا
        تامولعملاو Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 21
        نم 74 زمر ةيعونلا ةيعون ررقملا يلامجإ ددع تاعاسلا ةيعونلا ةبسن تاعاس
        ةيعونلا NARS (2010) A Humanities, ethical and Social Sciences (Univ.
        Req.) 12 9.35% 8‐10% B Mathematics and Basic Sciences 24 17.27% 16‐18% C
        Basic Computing Sciences (institution req.) 42 30.22% 26‐28% D Applied
        Computing Sciences (specialization) 42 30.22% 28‐30% E Training ( ئفاكت
        6 )يفيص مرت 4.32% 3‐5% F Projects 6 4.32% 3‐5% Subtotal 127 91.37%
        84‐96% G Optional (Institution character-identifying subjects) 12 8.63%
        16‐4% Total 138 100.00% 100% لمتشت مئاوقلا ةيلاتلا ىلع تاررقملا ةيساردلا
        ةفلتخملا احضوم اهب ددع تاعاسلا ةدمتعملا لكل ررقم امو اهرظاني نم تاعاسلا
        ةيلعفلا نم تارضاحملا امو اهمعدي نم لماعملا .نيرامتلاو ةعماج قيزاقزلا– لك
        ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 22
        نم 74 لوادجلا ةيساردلا :لاوأ تابلطتملا ةماعلا )ةعماجلا تابلطتم( ( 12 )
        ةعاس ةدمتعم (6 ةعاس يرابجا و6 ةعاس )يرايتخا أ. تاررقملا ةيرابجلاا (6)
        تاعاس ةدمتعم ةيرابجا ةمسقم امك :يلي مقر ررقملا مسا ررقملا ددع تاعاسلا
        ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا HU100 English ةغللا
        ةيزيلجنلإا 2 2 - --- HU101 Report Writing and Presentation Skills ةباتك
        ريراقتلا تاراهمو ضرعلا 2 2 - HU100 HU102 Social, Ethical, and
        Professional Issues in Computing اياضقلا ةيعامتجلاا ةيقلاخلأاو ةينهملاو
        يف ةبسوحلا 2 2 - --- *HU103 Human Rights and Anticorruption داسفلا
        ةحفاكمو ناسنلاا قوقح 0 2 - --- ب. تاررقملا ةيرايتخلاا (6) تاعاس ةدمتعم
        اهراتخي بلاطلا نم نيب تاررقملا ةيرايتخلاا ةيلاتلا مقر ررقملا مسا ررقملا
        ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا HU104
        Organizational Behavior كولسلا يميظنتلا 2 2 - --- HU105 Communication
        and Negotiation Skills تاراهم لاصتلاا ضوافتلاو 2 2 - --- HU106 Strategic
        Planning طيطختلا يجيتارتسلاا 2 2 - --- HU107 Fundamentals of Management
        تايساسأ ةرادلإا 2 2 - --- HU108 Fundamentals of Economics and
        Feasibility Study تايساسأ داصتقلاا تاساردو ىودجلا 2 2 - --- * بلاطلل
        يمكارتلا لدعملا وا يلكلا عومجملا يف بسحت لاو بوسرو حاجن ةدام ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 23
        نم 74 HU109 Business Management ةرادإ لامعلاا 2 2 - --- HU110
        Entrepreneurship and Innovation ةداير لامعلاا راكتبلااو 2 2 - --- HU111
        Digital Marketing قيوستلا يمقرلا 2 2 - --- HU112 Fundamentals of
        Accounting ئدابم ةبساحملا 2 2 - --- HU113 Fundamentals of Grant Writing
        تايساسأ ةباتك حنملا 2 2 - --- :ايناث تابلطتم ةيلكلا ( 66 ) ةعاس ةدمتعم
        أ- مولع ةيساسأ ( 24 ةعاس ةدمتعم )ةيرابجا مقر ررقملا مسا ررقملا ددع
        تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا BS100 Mathematics I
        تايضايرلا I 3 2 2 --- BS101 Mathematics II تايضايرلا II 3 2 2 BS100
        BS102 Physics for Computing ف ءايزي بساحلا 3 2 2 --- BS103 Probability
        and Statistics ءاصحلإا تلاامتحلااو 3 2 2 BS100 BS201 Mathematics III
        تايضايرلا III 3 2 2 BS101 IT100 Digital Logic Design يقطنملا ميمصتلا 3 2
        2 --- DS100 Operations Research تايلمعلا ثوحب 3 2 2 --- CS103 Discrete
        Structures ةددحم بيكارت 3 2 2 BS100 ب- مولع بساح ةيساسأ ( 42 ةعاس ةدمتعم
        )ةيرابجا مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع
        بلطتملا قباسلا CS100 Introduction to Computational Thinking سحلا ريكفتلا
        يف ةمدقم ايب 3 2 2 --- CS101 Computer Programming I ةجمرب بساحلا I 3 2 2
        --- CS102 Computer Programming II ةجمرب بساحلا II 3 2 2 CS101 ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 24
        نم 74 CS200 Data Structures لكايه تانايبلا 3 2 2 CS102 IT200 Computer
        Organization and Architecture بساحلا ميظنتو ةيرامعم 3 2 2 IT100 IS200
        Database Systems تانايبلا دعاوق مظن 3 2 2 CS102 IT201 Computer Graphics
        بساحلاب مسرلا 3 2 2 BS101 CS201 Operating Systems ليغشتلا مظن 3 2 2
        CS100 IS201 Systems Analysis and Design ميمصتو ليلحت لا مظن 3 2 2 IS200
        CS202 Analysis and Design of Algorithms تايمزراوخلا ميمصتو ليلحت 3 2 2
        CS200 IT203 Multimedia ةددعتملا طئاسولا 3 2 2 IT201 CS203 Artificial
        Intelligence يعانطصلاا ءاكذلا 3 2 2 CS200 IT202 Computer Networks تاكبش
        بساحلا 3 2 2 CS201 IT301 Web and Network Programming تاكبشلا ةجمرب و
        تنرتنلاا 3 2 2 CS200 :اثلاث تابلطتم صصختلا 60 ةعاس ةدمتعم ( 42 ةعاس
        يرابجإ + 12 ةعاس يرايتخا + 6 عورشم )جرخت :ًلاوا مولع بساحلا . أ-
        تاررقملا ةيرابجلاا ( 42 ) تاعاس ةدمتعم ةيرابجا ةمسقم امك :يلي مقر ررقملا
        مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا
        CS300 Human Computer Interaction لعافتلا نيب ناسنلاا ةللااو 3 2 2 CS200
        CS301 Systems Programming ةجمرب مظنلا 3 2 2 CS201 CS302 Formal Languages
        تاغللا ةيروصلا 3 2 2 CS200 CS303 Machine Learning لآا ملعت ةل 3 2 2
        CS203 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 25
        نم 74 CS304 Software Testing and Maintenance رابتخا ةنايصو تايجمربلا 3 2
        2 IS201 DS300 Software Project Management ةرادإ تايجمربلا تاعورشم 3 2 2
        DS100 IS303 Introduction to Big Data ةمخضلا تانايبلا يف ةمدقم 3 2 2
        IS200 IT303 Introduction to Cloud Computing ةيباحسلا ةبسوحلا يف ةمدقم 3
        2 2 CS200 IT304 Introduction to Cybersecurity يناربيسلا نملأا يف ةمدقم 3
        2 2 CS200 IT302 Introduction to Internet of Things ءايشلأا تنرتنإ يف
        ةمدقم 3 2 2 CS200 CS400 High Performance Computing ءادلأا ةقئاف ةبسوحلا
        3 2 2 CS201 CS401 Compiler Design تامجرتملا ميمصت 3 2 2 CS200 CS402
        Distributed and Concurrent Algorithms ةنمازتملاو ةعزوملا تايمزراوخلا 3 2
        2 CS202 CS403 Neural networks and deep learning قيمعلا ملعتلاو ةيبصعلا
        تاكبشلا 3 2 2 CS203 ب- تاررقملا ةيرايتخلاا ( 12 ) تاعاس ةدمتعم اهراتخي
        بلاطلا نم نيب تاررقملا ةيرايتخلاا ةيلاتلا مقر ررقملا مسا ررقملا ددع
        تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا CS404
        Bioinformatics ةيويحلا ةيتامولعملا 3 2 2 CS202 CS405 Fuzzy Logic and
        Intelligent Systems ةيكذلا ةمظنلأاو يبابضلا قطنملا 3 2 2 CS203 CS406
        Software Design and Architecture ةيرامعم و ميمصت تايجمربلا 3 2 2 IS201
        CS407 Natural Language Processing ةجلاعم تاغللا ةيعيبطلا 3 2 2 CS303
        CS408 Soft Computing ةبسوحلا ةنرملا 3 2 2 CS203 IT305 Mobile Application
        Development لومحملا فتاهلا تاقيبطت ريوطت 3 2 2 CS200 CS409 Introduction
        to Cryptography ريفشتلا يف ةمدقم 3 2 2 IT304 CS410 Theory of Computation
        تاباسحلا ةيرظن 3 2 2 CS302 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو
        Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 26
        نم 74 CS411 Programming Language Design ةجمربلا تاغل ميمصت 3 2 2 CS200
        CS412 Intelligent Agents ءايكذأ ءلامع 3 2 2 CS203 CS413 Computer Systems
        Performance يللاا بساحلا مظن ءادأ 3 2 2 CS201 CS425 Selected Topics in
        Computer Science I يف ةراتخم تاعوضوم بساحلا مولع I 3 2 2 --- CS426
        Selected Topics in Computer Science II يف ةراتخم تاعوضوم مولع بساحلا II
        3 2 2 --- ج- عورشم جرختلا (6 تاعاس )ةدمتعم مقر ررقملا مسا ررقملا ددع
        تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا CS430 Project I
        عورشملا I 3 1 4 --- CS431 Project II عورشملا II 3 1 4 CS430 :ً ايناث مظن
        تامولعملا . أ- تاررقملا ةيرابجلاا ( 42 ) تاعاس ةدمتعم ةيرابجا ةمسقم امك
        :يلي مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع
        بلطتملا قباسلا IS300 Database Management Systems مظن ةرادإ دعاوق تانايب
        3 2 2 IS200 GI300 Geographical Information Systems مظن تامولعملا
        ةيفارغجلا 3 2 2 IS200, IS201 IS301 Intelligent Systems and Business
        Intelligence ةيكذلا ةمظنلأا و ءاكذ لامعلاا 3 2 2 IS201 DS300 Software
        Project Management ةرادإ تايجمربلا تاعورشم 3 2 2 DS100 IS303
        Introduction to Big Data ةمخضلا تانايبلا يف ةمدقم 3 2 2 IS200 IT303
        Introduction to Cloud Computing ةيباحسلا ةبسوحلا يف ةمدقم 3 2 2 CS200
        IT304 Introduction to Cybersecurity يناربيسلا نملأا يف ةمدقم 3 2 2 CS200
        IT302 Introduction to Internet of Things ءايشلأا تنرتنإ يف ةمدقم 3 2 2
        CS200 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 27
        نم 74 IS304 E-Business ةينورتكللإا لامعلأا 3 2 2 IS201 IS400 Big Data
        Analytics لا تانايبلا تلايلحت ةمخض 3 2 2 IS303 IS401 Information
        Security تامولعملا نمأ 3 2 2 IT304 IS402 Distributed and Mobile
        Databases ةلومحملاو ةعزوملا تانايبلا دعاوق مظن 3 2 2 IS200 IS406
        Information Retrieval عاجرتسا تامولعملا 3 2 2 IS201 IS405 Data Mining
        and Machine Learning يف بيقنتلا للآا ملعتلاو تانايبلا ة 3 2 2 IS201 ب-
        تاررقملا ةيرايتخلاا ( 12 ) تاعاس ةدمتعم اهراتخي بلاطلا نم نيب تاررقملا
        ةيرايتخلاا ةيلاتلا مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم
        نيرامت / يلمع بلطتملا قباسلا IS403 Blockchain Technologies لتكلا ةلسلس
        ةينقت 3 2 2 IT304 IS404 Web and Social Media Analytics ةيعامتجلاا
        طئاسولاو بيولا تلايلحت 3 2 2 IS400 IS407 Data Visualization ضرع يئرم
        تانايبلل 3 2 2 IS303 IS408 Software Engineering تايجمربلا ةسدنه 3 2 2
        IS201 IS409 Database Administration تانايبلا دعاوق ةرادإ 3 2 2 IS201
        IS410 Managing Digital Firms رشلا ةرادإ تاك مقرلا ةي 3 2 2 IS200 IS411
        Data Warehousing تانايبلا تاعدوتسم 3 2 2 IS300 IS412 Business Process
        Management لامعلأا تايلمع ةرادإ 3 2 2 IS201 IS413 Web-based Information
        Systems تنرتنلإا ةكبش ىلع تامولعملا مظن 3 2 2 IT301 CS300 Human Computer
        Interaction ةللااو ناسنلاا نيب لعافتلا 3 2 2 CS200 IS414 Business
        Analytics Programming لامعلأا ليلحت ةجمرب 3 2 2 CS200 IS415 Enterprise
        Information Systems ةسسؤملا تامولعم مظن 3 2 2 IS201 ةعماج قيزاقزلا– لك
        ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 28
        نم 74 IS416 Information Systems Auditing قيقدت تامولعملا مظن 3 2 2 IS201
        IS417 Multimedia Information Systems ةددعتملا طئاسولا تامولعم مظن 3 2 2
        IS201 IS418 Software Requirements Analysis ليلحت تايجمربلا تابلطتم 3 2 2
        IS201 IS419 Systems Design Patterns مظنلا ميمصت طامنأ 3 2 2 IS201 IS420
        Knowledge Management ةفرعملا ةرادإ 3 2 2 IS201 IS421 Cloud Databases
        ةيباحسلا تانايبلا دعاوق 3 2 2 IT303 IS422 Information Systems Quality
        Assurance تامولعملا مظن ةدوج نامض 3 2 2 IS201 IS425 Selected Topics in
        Information Systems I يف ةراتخم تاعوضوم تامولعملا مظن I 3 2 2 --- IS426
        Selected Topics in Information Systems II يف ةراتخم تاعوضوم تامولعملا
        مظن II 3 2 2 --- ج- عورشم جرختلا (6 تاعاس )ةدمتعم مقر ررقملا مسا ررقملا
        ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا IS430 Project I
        عورشملا I 3 1 4 --- IS431 Project II عورشملا II 3 1 4 IS430 :ً اثلاث لا
        ويج ةيتامولعم . أ- تاررقملا ةيرابجلاا ( 42 ) تاعاس ةدمتعم ةيرابجا ةمسقم
        امك :يلي مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع
        بلطتملا قباسلا IS300 Database Management Systems مظن تانايب دعاوق ةرادإ
        3 2 2 IS200 GI300 Geographical Information Systems مظن تامولعملا
        ةيفارغجلا 3 2 2 IS200, IS201 DS300 Software Project Management ةرادإ
        تايجمربلا تاعورشم 3 2 2 DS100 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو
        Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 29
        نم 74 IS303 Introduction to Big Data ةمخضلا تانايبلا يف ةمدقم 3 2 2
        IS200 IT303 Introduction to Cloud Computing ةمدقم يف ةبسوحلا ةيباحسلا 3
        2 2 CS200 IT304 Introduction to Cybersecurity ةمدقم يف نملأا يناربيسلا 3
        2 2 CS200 IT302 Introduction to Internet of Things ةمدقم يف تنرتنإ
        ءايشلأا 3 2 2 CS200 GI301 Principles of Remote Sensing ئدابم راعشتسلاا
        دعب نع 3 2 2 GI300 GI302 Spatial Database دعاوق ةيناكملا تانايبلا 3 2 2
        GI300 GI400 Digital Cartography and Visualization ديسجتلاو ةيمقرلا
        ايفارجوتراكلا يئرملا 3 2 2 GI300 GI401 Spatial Analysis and Modeling
        ةجذمنلاو ليلحتلا ةيناكملا 3 2 2 GI300 GI402 GIS Programming تامولعملا
        مظن ةجمرب ةيفارغجلا 3 2 2 GI300 GI403 Spatial Decision Support Systems
        تارارقلا معد مظن ةيناكملا 3 2 2 GI300 GI404 Web and Mobile GIS بيولا ىلع
        ةيفارغجلا تامولعملا مظن لومحملاو 3 2 2 GI300, IT301 ب- تاررقملا
        ةيرايتخلاا ( 12 ) تاعاس ةدمتعم اهراتخي بلاطلا نم نيب تاررقملا ةيرايتخلاا
        ةيلاتلا مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع
        بلطتملا قباسلا GI405 Spatial Statistics يناكملا ءاصحلاا 3 2 2 GI300
        GI406 Open and Distributed GIS Infrastructures تامولعملا مظنل ةيتحتلا
        ةينبلا ةعزوملا ةيفارغجلا ةحوتفملاو 3 2 2 GI300 GI407 Medical GIS ةيبطلا
        ةيفارغجلا تامولعملا مظن 3 2 2 GI300 GI408 GIS Network Modeling ةيفارغجلا
        تامولعملا مظن مادختساب تاكبشلا ةجذمن 3 2 2 GI300 GI409 Spatial Data
        Mining يف بيقنتلا ةيناكملا تانايبلا 3 2 2 IS201 ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 30
        نم 74 IS403 Blockchain Technologies لتكلا ةلسلس ةينقت 3 2 2 IT304 GI410
        Remote Sensing Application دعب نع راعشتسلاا تاقيبطت 3 2 2 GI301 GI411
        Spatial Big Data ةمخضلا ةيناكملا تانايبلا 3 2 2 IS303 GI412 GIS and
        Cyber Security نملأاو ةيفارغجلا تامولعملا مظن يناربيسلا 3 2 2 IT304
        GI413 Cloud GIS ةيباحسلا ةيفارغجلا تامولعملا مظن 3 2 2 IT303 GI414
        Spatial Data Acquisition Techniques تانايبلا عمج تاينقت ةيناكملا 3 2 2
        GI300 GI425 Selected Topics in Geoinformatics I لا يف ةراتخم تاعوضوم ويج
        ةيتامولعم I 3 2 2 --- GI426 Selected Topics in Geoinformatics II لا يف
        ةراتخم تاعوضوم ويج ةيتامولعم II 3 2 2 --- ج- عورشم جرختلا (6 تاعاس
        )ةدمتعم ررقملا مقر ررقملا مسا تاعاسلا ددع ةدمتعملا ةرضاحم / نيرامت يلمع
        بلطتملا قباسلا GI430 Project I عورشملا I 3 1 4 --- GI431 Project II
        عورشملا II 3 1 4 GI430 :ً اعبار ايجولونكت .تامولعملا أ- تاررقملا
        ةيرابجلاا ( 42 ) تاعاس ةدمتعم ةيرابجا ةمسقم امك :يلي مقر ررقملا مسا
        ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا IS303
        Introduction to Big Data ةمخضلا تانايبلا يف ةمدقم 3 2 2 IS200 IT303
        Introduction to Cloud Computing ةمدقم يف ةبسوحلا ةيباحسلا 3 2 2 CS200
        IT304 Introduction to Cybersecurity ةمدقم يف نملأا يناربيسلا 3 2 2 CS200
        IT302 Introduction to Internet of Things ةمدقم يف تنرتنإ ءايشلأا 3 2 2
        CS200 DS300 Software Project Management ةرادإ تايجمربلا تاعورشم 3 2 2
        DS100 IT300 Digital Signal Processing ةجلاعم تاراشلإا ةيمقرلا 3 2 2
        BS101 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University –
        Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 31
        نم 74 IT305 Mobile Application Development ريوطت تاقيبطت لومحملا 3 2 2
        CS200 IT306 Wireless and Mobile Networks تاكبشلا ةيكلسلالا ةكرحتملاو 3 2
        2 IT202 IT307 Embedded Systems Design ميمصت مظنلا ملا ةجمد 3 2 2 IT200
        IT308 Microcontrollers ةريغصلا تامكحتملا 3 2 2 CS200 IT401 Image
        Processing روصلا ةجلاعم 3 2 2 IT300 IT402 Network Security تاكبشلا ناما
        3 2 2 IT304, IT202 IT403 Computer Vision ةيؤرلا بساحلاب 3 2 2 IT203
        IT404 Robotics يللآا ناسنلإا 3 2 2 IT200 ب- تاررقملا ةيرايتخلاا ( 12 )
        تاعاس ةدمتعم اهراتخي بلاطلا نم نيب تاررقملا ةيرايتخلاا ةيلاتلا مقر
        ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا
        قباسلا IT405 Computer Animation and 3D Modeling داعبلأا ةيثلاث ةجذمنلاو
        ةكرحتملا موسرلا 3 2 2 IT203 IT406 Information Theory and Data
        Compression تانايبلا طغضو تامولعملا ةيرظن 3 2 2 IT202 CS303 Machine
        Learning لآا ملعت ةل 3 2 2 CS203 IT407 Virtual and Augmented Reality
        ززعملاو يضارتفلاا عقاولا 3 2 2 IT203 IT408 Network Simulation تاكبشلا
        ةاكاحم 3 2 2 IT202 IT409 Computer Forensics ةيبوساحلا ةيئانجلا مولعلا 3
        2 2 IT200 CS301 Systems Programming مظنلا ةجمرب 3 2 2 CS201 CS300 Human
        Computer Interaction ةللااو ناسنلاا نيب لعافتلا 3 2 2 CS200 IS417
        Multimedia Information Systems ةددعتملا طئاسولا تامولعم مظن 3 2 2 IS201
        ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty
        of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 32
        نم 74 IT410 Cloud Networks ةيباحسلا تاكبشلا 3 2 2 IT202 IT411 Pattern
        Recognition طامنلأا ىلع فرعتلا 3 2 2 IT203 IT412 Multimedia Mining يف
        بيقنتلا ةددعتملا طئاسولا 3 2 2 IT203 IT413 Optical Networks تاكبشلا
        ةيئوضلا 3 2 2 IT202 IT414 Quantum Computing مكلا ةبسوح 3 2 2 CS201 IT415
        Computational Imaging يباسحلا ريوصتلا 3 2 2 IT401 IT416 Game Development
        باعللأا ريوطت 3 2 2 CS200 IT417 Wireless Sensors Networks تاكبش
        راعشتسلاا ةيكلسلالا 3 2 2 IT202 IT425 Selected Topics in Information
        Technology I يف ةراتخم تاعوضوم ايجولونكت تامولعملا I 3 2 2 --- IT426
        Selected Topics in Information Technology II يف ةراتخم تاعوضوم ايجولونكت
        تامولعملا II 3 2 2 --- ج- عورشم جرختلا (6 تاعاس )ةدمتعم ررقملا مقر
        ررقملا مسا تاعاسلا ددع ةدمتعملا ةرضاحم / نيرامت يلمع بلطتملا قباسلا
        IT430 Project I عورشملا I 3 1 4 --- IT431 Project II عورشملا II 3 1 4
        IT430 :ً اسماخ معد رارقلا . أ- تاررقملا ةيرابجلاا ( 42 ) تاعاس ةدمتعم
        ةيرابجا ةمسقم امك :يلي مقر ررقملا مسا ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم
        نيرامت / يلمع بلطتملا قباسلا DS301 Modeling and Simulation ةجذمنلا
        ةاكاحملاو 3 2 2 DS100 BS103 DS300 Software Project Management ةرادإ
        تايجمربلا تاعورشم 3 2 2 DS100 IS303 Introduction to Big Data ةمخضلا
        تانايبلا يف ةمدقم 3 2 2 IS200 ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو
        Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 33
        نم 74 IT303 Introduction to Cloud Computing ةمدقم يف ةبسوحلا ةيباحسلا 3
        2 2 CS200 IT304 Introduction to Cybersecurity ةمدقم يف نملأا يناربيسلا 3
        2 2 CS200 IT302 Introduction to Internet of Things ةمدقم يف تنرتنإ
        ءايشلأا 3 2 2 CS200 DS302 Advanced operations Research ةمدقتملا تايلمعلا
        ثوحب 3 2 2 DS100 DS303 Decision Support Methodologies رارقلا معد تايجهنم
        3 2 2 DS100 DS305 Systems Dynamics Modeling مظنلا ةجذمن ةيكيمانيدلا 3 2
        2 DS301 DS400 Data Analytics تانايبلا ليلحت 3 2 2 DS100 DS401 Stochastic
        Methods لا ةيئاوشعلا قرط 3 2 2 DS100 DS403 Computational Intelligence
        ءاكذلا يباسحلا 3 2 2 CS203 DS404 Supply Chain Planning and Logistics
        ةيتسجوللا تامدخلاو ديروتلا ةلسلس طيطخت 3 2 2 DS100 DS405 Forecasting
        Techniques ؤبنتلا تاينقت 3 2 2 DS100 ب- تاررقملا ةيرايتخلاا ( 12 ) تاعاس
        ةدمتعم اهراتخي بلاطلا نم نيب تاررقملا ةيرايتخلاا ةيلاتلا مقر ررقملا مسا
        ررقملا ددع تاعاسلا ةدمتعملا ةرضاحم نيرامت / يلمع بلطتملا قباسلا DS304
        Quality Management ةدوجلا ةرادا 3 2 2 DS100 DS402 Strategic Management
        and Business Analysis لامعلأا ليلحتو ةيجيتارتسلاا ةرادلإا 3 2 2 DS303
        DS406 Service Management تامدخلا ةرادإ 3 2 2 DS407 Operations Management
        تايلمعلا ةرادإ 3 2 2 DS100 DS408 Multi-Objective Programing فادهلاا
        ةددعتم ةجمربلا 3 2 2 DS100 DS409 Production Planning and Inventory
        Control خملا ةبقارمو جاتنلإا طيطخت نزا 3 2 2 DS303 ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 34
        نم 74 DS410 Decision and Game Theory باعللأاو تارارقلا ةيرظن 3 2 2 DS303
        DS411 Decision and Risk Management رطاخملا ةرادإو رارقلا 3 2 2 DS303
        DS412 Optimization Models and Methods قرطو جذامن ةلثملأا 3 2 2 DS403
        DS413 Scheduling Techniques ةلودجلا تاينقت 3 2 2 DS100 DS414 Quality
        Control and Reliability ةيقوثوملاو ةدوجلا ةبقارم 3 2 2 DS304 DS415
        Stochastic Processes and Queuing Models راظتنلاا جذامنو ةيئاوشعلا
        تايلمعلا 3 2 2 DS100 DS416 Data Analytics Programming ةجمرب تانايبلا
        ليلحت 3 2 2 DS400 CS416 Fuzzy Logic and Intelligent Systems ةيكذلا
        ةمظنلأاو يبابضلا قطنملا 3 2 2 CS203 DS417 Decision Analysis رارقلا ليلحت
        3 2 2 DS303 DS418 Network Modelling and Optimization ةجذمن هيلثمأو كبشلا
        تا 3 2 2 DS301 DS419 Advanced Project Management ةمدقتملا تاعورشملا
        ةرادإ 3 2 2 DS300 IS405 Data Mining and Machine Learning يف بيقنتلا للآا
        ملعتلاو تانايبلا ة 3 2 2 IS201 DS425 Selected Topics in Operations
        Research and Decision Support I معدو تايلمعلا ثوحب يف ةراتخم تاعوضوم
        رارقلا I 3 2 2 --- DS426 Selected Topics in Operations Research and
        Decision Support II و تايلمعلا ثوحب يف ةراتخم تاعوضوم معد رارقلا II 3 2
        2 --- ج- عورشم جرختلا (6 تاعاس )ةدمتعم ررقملا مقر ررقملا مسا تاعاسلا ددع
        ةدمتعملا ةرضاحم / نيرامت يلمع بلطتملا قباسلا DS430 Project I عورشملا I 3
        1 4 --- DS431 Project II عورشملا II 3 1 4 DS430 ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 35
        نم 74 ىوتحملا يملعلا تاررقملل ةيساردلا لاوا : ىوتحملا يملعلا تاررقملل
        ةماعلا HU100: English The material reflects the stylistic variety that
        advanced learners must be able to deal with. The course gives practice
        in specific points of grammar to consolidate and extend learners
        existing knowledge. The course aims at developing proficiency in
        speaking, listening, reading, and writing of English. It is generalized
        as a remedial course for students whose English need considerable
        repair. The contents include parts of speech, count and uncountable
        nouns and articles, agreement between subject and verb, adverbs of
        frequency, tense and the sequence of tenses, active and passive voices,
        types of sentences, prepositions: time, place, action, directions,
        questions forms, multi-word verbs, capitalization. HU101: Report Writing
        and Presentation Skills Professionals in the public and private sector
        increasingly need to document and present complex technical findings,
        analysis and recommendations for effective decision making. This course
        is specially designed to focus on the unique needs of technical
        professionals who write and present to both technical and non-technical
        readers and decision makers. The course will demonstrate how technical
        professionals can use their expertise and knowledge to write
        grammatically accurate and successful technical reports and use
        persuasive communication for effective data presentation. This course
        introduces basic rudiments of report writing. The rationale for report
        writing, the structure of reports, physical appearance and linguistic.
        The course provides training on the fundamentals of writing and
        reviewing technical reports that help disseminate critical findings and
        recommendations effectively to clients in the associated business and
        government sector. In addition, it will encourage participants to give
        effective data presentations by helping them to speak with conviction
        and authority and assist them in selecting notes and visual aids that
        support their message. HU102: Social, Ethical, and Professional Issues
        in Computing The course examines Social, ethical, and professional
        issues facing computing professionals; ethical principles; discussion of
        case studies. The course introduces social and ethical issues that arise
        in the development and application of computing technology in modern
        society, alternate points of view and broader perspectives in the
        analysis of social and ethical concerns arising in the context of
        computing technology, the immediate and long-term implications to
        society in the creation and use of computing technology, the potential
        benefits and risks of computing technology to society, both locally and
        globally, the impact of computing technology on the economy at large as
        well as long-term trends, the codes of ethics of one or more
        professional societies related to computing technologies (e.g., ACM,
        IEEE, CISSP), the distinction between a profession and a trade, and how
        this distinction relates to ethics and responsibility, legal issues that
        computing professionals may encounter as part of their practice. The
        course introduces basic تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج Zagazig
        University – Faculty of Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        human rights philosophy, principles, instruments and institutions, and
        an overview of current issues and debates in the field with focus on the
        problems specific to Egypt. Topics may include: definition of human
        rights, historical development of the concept of human rights, culture
        relativism versus universally accepted human rights standards, various
        human rights: personal, political, civil, social, economic, … etc.,
        covering human rights within official international organizations,
        influence of business and global economic restructuring on human rights,
        monitoring human rights, human rights violations and Anti-Corruption.
        HU104: Organizational Behavior Perception, learning, motivation and
        value; individual differences and work performance; understanding
        yourself; motivating yourself and others, working within groups,
        achieving success through goal setting, achieving high personal
        productivity and quality; achieving rewarding and satisfying career;
        communicating with people; leading and influencing others; building
        relationships with supervisors, co-worker and customers. HU105:
        Communication and Negotiation Skills The goal is to become knowledgeable
        of the Integrated and Collaborative Engagement Process and the theory
        and practice of effective relationship building by developing a critical
        thinking process that creates an understanding of diverse constructions
        of reality shared by individuals and groups in any setting. Effective
        Business Communication, Communicating in Teams & Business Etiquette,
        Communicating Intercultural, Planning Business Messages, Writing
        Business Messages, Completing Business Messages, Writing Routine
        Messages, Writing Bad News Messages Writing Persuasive Messages,
        Planning Business Reports, Writing Business Reports, Completing Business
        Reports, Oral Presentations, Writing Resumes and Application Letters,
        Interviewing for Employment, and Negotiation Skills book HU106:
        Strategic Planning In this strategic planning course, student will gain
        an understanding of the relationships between strategy, planning and
        execution, and learn how to develop a strategic mindset, improve your
        analytical skills, and apply creative planning to current challenges.
        There’s much confusion and controversy surrounding business strategy.
        Where is the magic pathway to success for organization? How do we
        optimize our strategy to use least resources to achieve our objectives?
        What are the variables and how do we juggle them when so much always
        seems to be changing? By the end of this course, student should be able
        to: understanding the definitions and components of strategic planning,
        identify why some organizations have great difficulty in strategic
        planning, discuss the role of strategic planning, assess and improve
        your company strategy, utilize your Improved ability to think
        strategically while operating and managing daily objectives, new ability
        to integrate strategic planning into normal business activity. HU107:
        Fundamentals of Management Introduction to management science,
        principals of organization structures and their categories, inventory
        models, analysis cost volume profit, objectives and methodologies of
        resource
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 36 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 37
        نم 74 management, skills needed to effective management renewable and
        natural resources. Decision making processes and financial management,
        accounting management, marketing, and human resource management. HU108:
        Fundamentals of Economics and Feasibility Studies Concepts of economics.
        The economic problem. Supply and demand. Theory of demand including
        utility theory, theory of production, theory of cost, theory of firm
        including pricing theory, economics of education, economic of science
        and technology, economics of automation including computerization.
        HU109: Business Management This course emphasizes the skills needed for
        managing a business that involves the selection and supervision of
        employees including efficient time, personnel, facilities, and financial
        resources. Students will explore forms of business ownership, typical
        business organizational structure; product or service promotion in
        business; effective communications, human relations skills required in
        dealing with employees, and effective management strategies used in
        personnel, finance, production, marketing, and information processing.
        HU110: Entrepreneurship and Innovation Entrepreneurship and innovation
        are increasingly important in all areas of business and government.
        Entrepreneurial start-ups galvanize the economy by identifying new
        opportunities and redirecting resources to them. Established firms
        innovate in order to outmaneuver or respond to their competition. And in
        the public sector, the need for effective policies to deal with new
        challenges and for increasing service delivery with declining budgets
        also places a premium on innovative thinking. The course focuses on the
        skills necessary for the planning, development and launch of
        entrepreneurial and innovative ventures. The material covered includes
        the foundations of entrepreneurship, techniques for creative thinking,
        and processes for developing, planning and launching a new venture
        including protecting intellectual property, evaluating markets,
        developing innovative business models, budgeting, and raising finance.
        The major piece of assessment is the writing of a comprehensive business
        plan. HU111: Digital Marketing Students learn how to apply digital
        marketing and analytic tools to create competitive and effective digital
        marketing programs. Topics include optimizing web and social content,
        online advertising, lead generation, email marketing, and web analytics.
        HU112: Fundamentals of Accounting This course introduces the students
        the fundamentals of accounting principles and practice applied to sole
        or single proprietorship which may be classified as service,
        merchandising and manufacturing businesses. It involves a study of
        Nature and Importance of Accounting, the accounts, Journal, and Ledger,
        Adjusting the accounts, Advisory Examination, Completion of ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 38
        نم 74 the Accounting Cycle, Accounting for Merchandising Operations,
        Accounting Information System, Manufacturing, Cash, Third grading
        Examination, and Departmental Examination. HU113: Fundamentals of Grant
        Writing This course introduces the skills of grant writing in
        informatics. Each student will submit a completed grant application as a
        culminating experience. This course introduces students to grant
        development and preparation so that they can participate in the process
        of obtaining public or private funds to support research, education
        and/or service projects. BS100: Mathematics I Graphs and Models, Linear
        Models and Rates of Change, Functions and Their Graphs, Review of
        Trigonometric Functions, Inverse Functions, Exponential and Logarithmic
        Functions, Limits and their properties (finding limits graphically and
        numerically, evaluating limits analytically, continuity and one-sided
        limits, infinite limits graphs and limits of trigonometric functions);
        differentiation (the derivative and the tangent line problem, basic
        differentiation rules and rates of change, product and quotient rules
        and higher-order derivatives, the chain rule, implicit differentiation,
        derivatives of inverse functions, related rates, newton's method );
        Applications of differentiation ;integration. (antiderivatives and
        indefinite integration, Area, Riemann sums and definite integrals, the
        fundamental theorem of calculus, integration by substitution,
        indeterminate forms and L’hopital’s rule, the natural logarithmic
        function, integration, inverse trigonometric functions, , hyperbolic
        functions) ; differential equations; (Slope fields and Euler’s method,
        growth and decay, separation of variables, the logistic equation,
        first-order linear differential equations); Applications of integration;
        Integration techniques and improper integrals (Basic integration rules,
        integration by parts, trigonometric integrals, numerical integration,
        integration by tables and other integration techniques, improper
        integrals ). BS101: Mathematics II Systems of linear equations (direct
        methods for solving linear systems, Gaussian elimination, partial
        pivoting; counting operations, spanning sets and linear independence,
        applications, iterative methods for solving linear systems); Matrices
        (matrix operations, matrix algebra, the inverse of a matrix, the LU
        factorization, subspaces, basis, dimension, and rank, linear
        transformations, canonical forms of matrices, applications in
        biomedical); Eigenvalues and Eigenvectors ( determinants, applications
        of determinants, eigenvalues and eigenvectors of n x n matrices,
        similarity and diagonalization, iterative methods for computing
        eigenvalues, applications and the perron-frobenius theorem);
        Orthogonality ( orthogonality in Rn, orthogonal complements and
        orthogonal projections, the gram-schmidt process and the QR
        factorization, approximating eigenvalues with the QR algorithm,
        orthogonal diagonalization of symmetric matrices, applications); Vector
        spaces( vector spaces and subspaces, linear independence, basis, and
        dimension, linear transformations, the kernel and range of a linear
        transformation, the matrix of a linear transformation, applications);
        Distance and Approximation( inner product spaces. explorations: vectors
        and matrices with complex entries; geometric inequalities and
        optimization problems, norms and distance functions. least squares
        approximation, the singular value decomposition, applications). ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 39
        نم 74 BS102: Physics for Computing This course aims to show the
        fundamental role of physics in building up computing systems and
        computer applications. You will be exposed to various selected physics
        topics (Optics, Electrics, Electronics, and Magnetism), with which, many
        useful physics‐computing systems have been developed and changed our
        daily life. This course serves you as an introductory general education
        course to encourage you for interdisciplinary thinking and exploration.
        This course introduces Electrical sources, electrical circuit elements,
        Ohm's law, Kirshoffs laws, solution of AC circuits, superposition
        theorem, substitution theorem, Thevenin's and Norton's theorems,
        compensation theorem, four-pole networks, electric power, maximum power
        transfer theorem, diodes, transistors, field effect transistors,
        operational amplifiers and their basic circuits and applications. BS103:
        Probability and Statistics Introduction to probability, properties of
        probability, methods of computing probability, probability distribution,
        sampling and sampling distribution. Review of sampling theory and
        distributions, point's estimates, confidence interval estimates. Tests
        of hypotheses and significance for large or small samples, operating
        characteristic curves, quality control chart, fitting theoretical
        distributions to sample frequency distributions, goodness of fit. Curve
        fitting, regression and correlation. Analysis of variance Students are
        instructed on the use of a statistics computer package at the beginning
        of them. Parametric classifiers, bays linear classify, linear classifier
        Design, clustering, parametric clustering, nonparametric clustering
        selection at representatives. BS201: Mathematics III Infinite series
        (sequences, series and convergence ,Taylor polynomials and
        approximations, power series, representation of functions by power
        series, Taylor and McLaurin series; Fourier series) ; conics, parametric
        equations, and polar coordinates; Functions of several variables ;
        Fourier transform; Laplace transform; Inverse Laplace transform via
        partial fraction expansion method; Properties of the Laplace transform;
        Z transform; Inverse Z transform via partial fraction expansion method;
        Properties of the Z transform; Codes (code vectors, error-correcting
        codes, dual codes, linear codes); solutions of nonlinear systems of
        equations; boundary-value problems for ordinary differential equations;
        solutions to partial differential equations. ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 40
        نم 74 ايناث تاررقمل يملعلا ىوتحملا : بساحلا مولع مسق CS100: Introduction
        to Computational Thinking Computational thinking (CT) is a
        problem-solving process with the aid of computer; i.e. formulating a
        problem and expressing its solution in such a way that a computer can
        effectively carry it out. It includes several characteristics, such as
        breaking a problem into small and repetitive ordered steps, logically
        ordering and analyzing data and creating solutions that can be
        effectively implemented as algorithms running on computer. As such,
        computational thinking is essential not only to the Computer Science
        discipline, it can also be used to support problem solving across all
        disciplines, including math, science, engineering, business, finance and
        humanities. The aim of this course is hence to take students with no
        prior experience of thinking in a computational manner to a point where
        they can derive simple algorithms and code the programs to solve some
        basic problems in their domain of studies. In addition, the course will
        include topics to appreciate the internal operations of a processor and
        raise awareness of the socio‐ethical issues arising from the
        pervasiveness of computing technology. The course also includes learn to
        design, write, debug, and run programs encoded in the Python language.
        Develop a working knowledge for how computers operate and how computer
        programs are executed. Evolve critical thinking and problem-solving
        skills using an algorithmic approach. Learn about the programmer’s role
        in the software development process. Translate real-world issues into
        computer-solvable problems. CS101: Computer Programming I Introduces the
        fundamental concepts of procedural programming. Topics include data
        types, control structures, functions, arrays, files, and the mechanics
        of running, testing, and debugging. The course also offers an
        introduction to the historical and social context of computing and an
        overview of computer science as a discipline. The course also includes
        Fundamental programming constructs: Syntax and semantics of a
        higher-level language; variables, types, expressions, and assignment;
        simple I/O; conditional and iterative control structures; functions and
        parameter passing; structured decomposition. Algorithms and
        problem-solving: Problem solving strategies; the role of algorithms in
        the problem-solving process; implementation strategies for algorithms;
        debugging strategies; the concept and properties of algorithms. CS102:
        Computer Programming II Introduces the concepts of object-oriented
        programming to students with a background in the procedural paradigm.
        The course begins with a review of control structures and data types
        with emphasis on structured data types and array processing. It then
        moves on to introduce the object oriented programming paradigm, focusing
        on the definition and use of classes along with the fundamentals of
        object-oriented design. Other topics include an overview of programming
        language principles, simple analysis of algorithms, basic searching and
        sorting techniques, and an introduction to software engineering issues.
        The course also includes Review of control structures, functions, and
        primitive data types. Object-oriented programming: Object-oriented
        design; encapsulation and information hiding; separation of behavior and
        implementation; تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج Zagazig
        University – Faculty of Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        classes, subclasses, and inheritance; polymorphism; class hierarchies.
        Fundamental computing algorithms: simple searching and sorting
        algorithms (linear and binary search, selection and insertion sort).
        Fundamentals of event-driven programming; Introduction to computer
        graphics: Using a simple graphics API; Overview of programming
        languages: History of programming languages; brief survey of programming
        paradigms CS103: Discrete Structures Introduces the foundations of
        discrete mathematics as they apply to computer science, focusing on
        providing a solid theoretical foundation for further work. Topics
        include functions, relations, sets, simple proof techniques, Boolean
        algebra, propositional logic, digital logic, elementary number theory,
        and the fundamentals of counting. The course also includes Introduction
        to logic and proofs: Direct proofs; proof by contradiction; mathematical
        induction. Fundamental structures: Functions (surjections, injections,
        inverses, composition); relations (reflexivity, symmetry, transitivity,
        equivalence relations); sets (Venn diagrams, complements, Cartesian
        products, power sets); pigeonhole principle; cardinality and
        countability. Boolean algebra: Boolean values; standard operations on
        Boolean values; de Morgan's laws. Propositional logic: Logical
        connectives; truth tables; normal forms (conjunctive and disjunctive);
        validity. Digital logic: Logic gates, flip-flops, counters; circuit
        minimization. Elementary number theory: Factorability; properties of
        primes; greatest common divisors and least common multiples; Euclid's
        algorithm; modular arithmetic; the Chinese Remainder Theorem. The course
        also includes Predicate logic: Universal and existential quantification;
        modus ponens and modus tollens; limitations of predicate logic.
        Recurrence relations: Basic formulae; elementary solution techniques.
        Graphs and trees: Fundamental definitions; simple algorithms; traversal
        strategies; proof techniques; spanning trees; applications. Matrices:
        Basic properties; applications. CS200: Data Structures Specification,
        representation, and manipulation of basic data structures: linked lists,
        arrays, stacks, queues, trees, strings, symbol tables, Huffman codes,
        optimal search trees, pattern matching, priority queues, heaps, hash
        tables. Storage allocation, garbage collection, compaction, reference
        counts, Sorting, graphs (graph traversal, directed graphs). List and
        string processing languages. Analysis of algorithms. Performance
        evaluation involving worst case, average and expected case, and
        amortized analysis. Students are required to write programs in several
        languages such as C++, C#, Java, or Pascal. CS201: Operating Systems
        This course will introduce operating system design and implementation.
        The course will start with a brief historical perspective of the
        evolution of operating systems over the last fifty years, and then cover
        the major components of most operating systems. This will include:
        Computer system structures, Operating system structures, Process and
        Process management: process synchronization and mutual exclusion; two-
        process solution and Dekker's algorithm, semaphores (producer- consumer,
        readers-writer, dining philosophers, etc.), Interprocess communication,
        Process synchronization, Deadlocks, thread management, CPU scheduling:
        multiprogramming and time-sharing, scheduling approaches (SJF, FIFO,
        round robin, etc.),
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 41 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        Memory hierarchy and management: with and without swapping, virtual
        memory-paging and segmentation, page replacement algorithms,
        implementation., Virtual memory, Secondary storage management, I/O
        device management, File system: interface and implementation, FS
        services, disk space management, directory and data structure,
        Protection and security, and Case studies: Linux and Windows. CS202:
        Analysis and Design of Algorithms An introduction to the design and
        analysis of algorithms. The course covers design techniques, such as
        dynamic programming and greedy methods, as well as fundamentals of
        analyzing algorithms for correctness and time and space bounds. Topics
        include advanced sorting and searching methods, graph algorithms and
        geometric algorithms, notion of an algorithm: big-O, small-O, theta and
        omega notations. Space and time complexities of an algorithm.
        Fundamental design paradigms: divide and conquer, branch and bound,
        backtracking, dynamic programming greedy methods, simulation. Theory of
        NP-completeness, notion of an intractable problem. Measures of
        approximation: ratio bound and relative error. Polynomial time
        approximation scheme. Illustrative examples: graph theory, computational
        geometry, optimization, numerical analysis and data processing. Other
        areas vary from year to year, and may include matrix manipulations,
        string and pattern matching, set algorithms, polynomial computations,
        and the fast Fourier transform. CS203: Artificial Intelligence This is
        an introductory AI course. Topics will include Artificial and human
        intelligence, Overview of Artificial Intelligence, Basic Problem-Solving
        Strategies, Heuristic Search, Problem Reduction and AND/OR Graphs,
        domains of AI- symbolic processing: semantic nets, modeling model-based
        reasoning, frames. Knowledge Representation, Representing Knowledge with
        If Then Rules. Inference Engines, Inference techniques: implication,
        forward and backward chaining, inference nets, predicate logic,
        quantifiers, tautology, resolution, and unification. Rule based systems:
        inference engine, production systems, problem solving, planning,
        decomposition, and basic search techniques. AI languages: symbolic and
        coupled processing prolog: objects and relations, compound goals,
        backtracking, search mechanism, dynamic databases, lisp, program
        structure and operations, functions, unification, memory models. Fields
        of AI: heuristics and game plying, automated reasoning, problem solving,
        computational linguistics and natural language processing, computer
        vision, intelligent agents, robotics AI based computer systems:
        sequential and parallel inference machines, relation between AI and
        artificial neural nets, fuzzy systems. CS300: Human Computer Interaction
        Design Introduction to Human-Computer Interaction, or how computers
        communicate with people. Methodology for designing and testing user
        interfaces, interaction styles (command line, menus, graphical user
        interfaces, virtual reality), interaction techniques (including use of
        voice, gesture, and eye movement), design guidelines, and user interface
        management system software. Comprehensive coverage of computer human
        interaction(CHI) importance, design, theories, and
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 42 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        future direction; modeling compute interfaces, empirical techniques for
        task analysis and interface design of interaction, The scope of HCI:
        Different theories and disciplines that contribute to HCI, HCI Analysis:
        User analysis, task analysis, environment and domain analysis, Human
        Cognitive Architecture: Perception, memory, problem solving, Dialogue
        design: Input, output devices and ergonomics; embedded systems; web
        usability; interfaces for mobile devices; future systems, CSCW,
        Influences on Design: Guidelines and standards in HCI; conceptual
        design, Prototyping in HCI: vertical, horizontal, full, throw-away
        prototypes, and Empirical evaluation: qualitative and quantitative
        methods of collecting data from users; the Usability Engineering
        approach; research topics in evaluation techniques. Students will design
        a small user interface, program a prototype, and then test the result
        for usability. CS301: Systems Programming Low-level programming; review
        of addresses, pointers, memory layout, and data representation; text,
        data, and bss segments; debugging and hex dumps; concurrent execution
        with threads and processes; address spaces; file names; descriptors and
        file pointers; inheritance; system calls and library functions; standard
        I/O and string libraries; simplified socket programming; building tools
        to help programmers; make and make files; shell scripts and quoting;
        Unix tools including sed, echo, test, and find; scripting languages such
        as awk; version control; object and executable files (.o and a.out);
        symbol tables; pointers to functions; hierarchical directories; and DNS
        hierarchy; programming embedded systems. CS302: Formal Languages and
        Automata Alphabets and languages. Finite representation of language.
        Deterministic and non-deterministic finite automata and their
        applications. Equivalence considerations. Regular expressions. Context
        free languages. Context-free grammars. Regular languages, pushdown
        automata. Properties of context-free languages. Determinism and parsing
        top-down parsing, and bottom-up parsing. Turing machines: Computing with
        Turing machines, combining Turing machines, and nondeterministic Turing
        machines. CS303: Machine Learning Machine Learning is concerned with
        computer programs that automatically improve their performance through
        experience. Machine Learning methods have been applied to problems such
        as learning to drive an autonomous vehicle, learning to recognize human
        speech, and learning strategies for game playing. This course covers the
        primary approaches to machine learning from a variety of fields,
        including inductive inference of decision trees, neural network
        learning, statistical learning methods, genetic algorithms, bayesian
        methods, explanation-based learning, and reinforcement learning CS304:
        Software Testing and Maintenance Techniques and methods for developing
        and extending correct, stable, maintainable and efficient software.
        Testing methodologies and their practical application in software
        development. Different aspects of testing: Black box testing where
        testing is done without knowledge of how
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 43 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        the program is written; white box testing where the developer tries to
        guarantee that every statement, execution path and method is executed
        during the testing and finally unit testing which is a practical design
        methodology where test cases are developed as each function or method is
        written. Software developing aids and methods such as code-inspection.
        Code and memory profiling as a support for program optimizing. CS400:
        High Performance Computing This course is an introductory course on
        high-performance computing. High-performance computing refers to a
        specialized use and programming of (parallel) supercomputers, computer
        clusters, and everything from software to hardware to speed up
        computations. The CPU clock speed of desktop and commodity processors
        has reached a maximum range, due to physical limitations. As a result,
        more advanced (and often creative) use of software and parallel hardware
        is required to further speed up processing. In this course you will
        learn how to write faster code that is highly optimized for modern
        multi-core processors and clusters, using modern software development
        tools, performance profilers, specialized algorithms, parallelization
        strategies, and advanced parallel programming constructs in OpenMP and
        MPI. CS401: Compiler Design The Structure of a Compiler course, Lexical
        Analyzer, LEX, Design of Lex, Top down Parsing, LL(1) Parsers, Bottom up
        Parsing, YACC, LR parsers, Syntax Directed Translation, Types and Type
        Checking, Run-Time Storage Administration and Symbol Table Management,
        Intermediate Code and Code Generation, Data-Flow Analysis, Code
        Optimizations, Architecture and recent development on compilers CS402:
        Distributed and Concurrent Algorithms Goals of the course: To present
        fundamental algorithms and impossibility results from the concurrent
        programming literature, and to cover techniques for formally specifying
        and verifying concurrent systems. Both message-passing and shared-memory
        models of concurrency will be considered. At the end of the course,
        students will have a general knowledge of the concurrent programming
        literature and will be able to develop new concurrent algorithms and
        verify their correctness. Perhaps the most important skill to be
        developed is the ability to intuitively “see” how or why a concurrent
        program works (a skill most students probably take for granted when it
        comes to sequential programs). In other words, this class will teach you
        how to \think" concurrently. CS403: Neural networks and deep learning
        Over the past few years, neural networks have enjoyed a major resurgence
        in machine learning, and today yield state-of-the-art results in various
        fields. This course introduces deep neural network models, and surveys
        some the applications of these models in areas where they have been
        particularly successful. The course covers feedforward networks,
        convolutional networks, recurrent and recursive networks, as well
        general topics such as input encoding and training
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 44 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        techniques. The course also provides acquaintance with some of the
        software libraries available for building and training deep neural
        networks. CS404: Bioinformatics Introduces bioinformatics concepts and
        practice. Topics include biological databases, sequence alignment, gene
        and protein structure prediction, molecular phylogenetics, genomics and
        proteomics. Students will gain practical experience with bioinformatics
        tools and develop basic skills in the collection and presentation of
        bioinformatics data, as well as the rudiments of programming in a
        scripting language. CS405: Fuzzy Logic and Intelligent Systems Fuzzy Set
        and Fuzzy Logic: motivation, possibilistic interpretation, basic
        concepts, set operations, fuzzy relations, and fuzzy inferences. Fuzzy
        Logic Applications: approximate reasoning, fuzzy arithmetic, linguistic
        models, decision theory, classification, and fuzzy controllers
        (development, tuning, compilation, deployment). Computational
        Intelligence (CI): hybrid systems based on fuzzy, neural and
        evolutionary computation. Case studies of real world industrial and
        financial applications. CS406: Software Design and Architecture This
        course is concerned with the principles and concepts of engineering of
        large software systems and programs. Software architecture is an
        abstraction of system details that helps in managing the inherent
        complexity of software systems development. Software architecture
        provides opportunities for early evaluation of user needs, analysis of
        requirements and design, and prediction of system properties.
        Architectural styles, views, notations, and description languages
        provide systematic frameworks for engineering decisions and design
        practices. The focus of the course is on advanced topics related to
        software architecture practices, technologies, and artifacts. Students
        participate in individual or group projects related to developing
        architectural representations of software systems. CS407 Natural
        Language Processing Foundations of the natural language processing,
        language data in corpora, levels of description: phonetics and
        phonology, morphology, syntax, semantics and pragmatics. Traditional vs.
        formal grammars: representation of morphological and syntactic
        structures, meaning representation. context-free grammars and their
        context-sensitive extensions, DCG (Definite Clause Grammars), CKY
        algorithm (Cocke-Kasami-Younger), chart-parsing. Problem of ambiguity.
        Electronic dictionaries: representation of lexical knowledge. Types of
        the machine-readable dictionaries. Semantic representation of sentence
        meaning. The Compositionality Principle, composition of meaning.
        Semantic classification: valence frames, predicates, ontologies,
        transparent intentional logic (TIL) and its application to semantic
        analysis of sentences. Pragmatics: semantic and pragmatic nature of noun
        groups, discourse structure, deictic expressions, verbal and non-verbal
        contexts. Natural language understanding: semantic representation,
        inference and knowledge representations.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 45 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        CS408: Soft Computing ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        Evolutionary computation (EC), neuro-computation (NC) and fuzzy logic
        (FL), are considered as three major components of the so-called soft
        computing. The main idea of soft computation is to make decisions based
        on rough (incomplete, noisy, uncertain) data. The computing technology
        which make decisions based on clean, clear and complete data is often
        called hard computing, although researchers in this field are not hard
        at all (they are the most intelligent and flexible people in the world).
        The human brain is a computing machine consisting of two parts. The left
        part is good at hard computing (logical thinking), and the right part is
        good at soft computing (heuristic thinking). During the last half
        century, we developed a lot of computers for assisting the left part of
        the brain. In this century, we will put more energy to make computers to
        assist the right part of the brain. CS409: Introduction to Cryptography
        Cryptography provides important tools for ensuring the privacy,
        authenticity, and integrity of the increasingly sensitive information
        involved in modern digital systems. Nowadays, core cryptographic tools,
        including encryption, message authentication codes, digital signature,
        key agreement protocols, etc., are used behind millions of daily on-line
        transactions. In this course, we will unveil some of the "magic" of
        cryptography. Modern Cryptography uses mathematical language to
        precisely pin down elusive security goals, design primitives and
        protocols to achieve these goals, and validate the security of designed
        primitives and protocols using mathematical proofs based on clearly
        stated hardness assumptions. Therefore, to learn cryptography, it is
        essential to understand its mathematical underpinning. In this class, we
        will see the inner working of cryptography for several core
        cryptographic tools, from encryption, to message authentication codes,
        to hash functions, to digital signatures, etc. CS410: Theory of
        Computation An introduction to the theoretical foundations of computing,
        including abstract models of computing machines, the grammars those
        machines recognize, and the corresponding classes of languages. Topics
        include: Church's thesis; Grammars, the M-recursive functions, and
        Turing computability of the M-recursive functions, The incompatibility:
        The halting problem, Turing innumerability, Turing acceptability, and
        Turing decidability, unsolvable problems about Turing machines and
        M-recursive functions, Computational complexity: Time-bounded Turing
        machines, Rate of growth of functions, NP- Completeness, The complexity
        hierarchy, The prepositional calculus: Syntax, Truth-assignment,
        Validity and satisfy, and Equivalence and normal forms compactness.
        CS411: Programming Language Design This course is an introduction to the
        principles which underlie the definition and implementation of
        programming languages. Study of modern programming language paradigms
        (procedural, functional, logic, object oriented). Introduction to the
        design and implementation of programming languages including syntax,
        semantics, data types and structures, control structures, and run-time
        environments.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 46 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 47
        نم 74 CS4112: Intelligent Agents This course gives a broad introduction
        to the new and rapidly expanding field of agent-based computing. It
        introduces the key concepts and models of the field, dealing both with
        the individual agents and with their interactions. Emphasis is placed on
        automated negotiation, cooperation and on-line auctions, and students
        are required to program a trading agent in Java which will compete in a
        class tournament within a simulated trading environment. CS413: Computer
        Systems Performance It introduces the main concepts and techniques
        needed to plan the capacity of computer systems, predict their future
        performance under different configurations, and design new applications
        that meet performance requirements. The course is mainly based on the
        use of analytic queuing network models of computer systems. These
        techniques are applied to study the performance of centralized,
        distributed, parallel, client/server systems, Web server and e-commerce
        site performance. The course also discusses performance measuring tools
        for operating systems such as Unix and Windows. CS425: Selected Topics
        in Computer Science I Selected Topics provides an opportunity to study a
        topic which is not included in the existing curriculum. This course
        examines one or more selected current issues in the area of Computer
        Science. Topics chosen for study will be by arrangement with the
        department. CS426: Selected Topics in Computer Science II Selected
        Topics provides an opportunity to study a topic which is not included in
        the existing curriculum. This course examines one or more selected
        current issues in the area of Computer Science. Topics chosen for study
        will be by arrangement with the department. CS430/CS431: Project This
        course will continue for two semesters. In the first semester, a group
        of students will select one of the projects proposed by the department
        and analyze the underlying problem. In the second semester, the design
        and implementation of the project will be conducted. The student will
        deliver oral presentations, progress reports, and a final report. ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 48
        نم 74 اثلاث تاررقمل يملعلا ىوتحملا : مسق تامولعملا مظن IS200: Database
        Systems The goal of this course is to introduce the main features of
        theory, and database application design and development. Logical design
        and conceptual modeling, physical database design strategies, relational
        data model and query languages, query optimization, transaction
        management and distributed databases. IS201: Systems Analysis and Design
        The analysis and design phases of system development life cycle are
        covered in detail Methodologies for systems analysis, specifications,
        and design are covered. Both the traditional structured and
        object-oriented methodologies systems. Emphasis is placed on
        well-written documentation as well as oral communication typically
        required during the software development life cycle. Project management
        tools are employed by students to monitor their progress and the costs
        associated with their projects. CASE tools are employed for data and
        information modeling and specification. IS300: Database Management
        Systems This course introduces Transaction management; query processing
        and optimization; organization of database systems, indexing,
        multi-dimensional data, similarity-based analysis, performance
        evaluation, new database applications. This course will cover the core
        principles and techniques of data and information management. The
        potential topics covered in class include processing and optimization of
        declarative queries, transactions, crash recovery, self-tuning database
        systems, data stream systems, information retrieval and Web data
        management, and data mining. GI300: Geographical Information Systems
        Provides an understanding of the theory, data models and associated
        issues (such as uncertainty) that underlie GIScience and the way these
        are applied to, and effect, spatial analysis and spatial data
        management. This course discusses GIS concepts and terminology, the role
        of GIS in spatial data management and digital mapping, the multipurpose
        cadastre and resource GIS, methods of data collection and input, data
        modelling and representation, storage and retrieval of spatial data,
        concepts of database systems, manipulation and analysis features of GIS.
        IS301: Business Intelligence This course covers the key concepts and
        applications of business intelligence (BI). Business and technology
        drivers are explained in order to provide students with the proper
        context in understanding how BI can provide business value and help
        companies use technology effectively in managing their businesses. An
        overview that includes the uses and users of business intelligence, as
        well as the type of applications and tools that may be deployed, help
        students better understand the business intelligence project lifecycle.
        Additional topics that complement ةعماج قيزاقزلا– لك ةي تابساحلا
        تامولعملاو Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 49
        نم 74 the understanding and application of business intelligence such as
        data warehousing (DW) are introduced. Using practical examples and
        hands-on exercises with real life applications present an opportunity to
        effectively illustrate technical concepts and techniques used in BI. By
        providing an opportunity to gain both business and technical
        perspective, students are better equipped to appreciate in ways
        information technologies can be implemented to drive business results.
        IS303: Introduction to Big Data Big Data is the hot new buzzword in IT
        circles. The proliferation of digital technologies with digital storage
        and recording media has created massive amounts of diverse data, which
        can be used for marketing and many other purposes. The concept of Big
        Data refers to massive and often unstructured data, on which the
        processing capabilities of traditional data management tools result to
        be inadequate. Big Data can take up terabytes and petabytes of storage
        space in diverse formats including text, video, sound, images, and more.
        The course gives an overview of the Big Data phenomenon, focusing then
        on extracting value from the Big Data using predictive analytics
        techniques. Upon successful completion of this course, students should
        be able to: Understand the Big Data phenomenon; Understand the main Big
        Data tools (Hadoop & Spark); Understand the potential use in a corporate
        environment. IS304: E-Business This course focuses on business process
        redesign and change the management in the context of e-business. Topics
        include impact of e-business on business models, channel relationships
        and the value chain, integration of emerging technologies with legacy
        systems, functional and inter organizational integration, and
        transaction cost issues. Applications include supply and selling chain
        management, customer relation management, enterprise resource planning,
        e-procurement, and knowledge tone applications. IS400: Big Data
        Analytics This course provides a comprehensive and rigorous introduction
        to big data analytics. It will describe the hardware/software
        infrastructures that are used today for big data (e.g., Hadoop, Hive)
        and the implications of these infrastructures for the accurate and
        efficient analysis of big data. Students will learn the mathematical,
        statistical, artificial intelligence, and modeling techniques that have
        been developed for analysis of big data, especially for health care
        applications. Also, it will describe the visualization techniques which
        are useful for displaying big data analysis results for meaningful
        interpretation of the results by humans. IS401: Information Security
        Introduction, identification and authentication, authorization rules.
        Data classification. Basic data encryption and decryption, different
        encryption and decryption techniques, different types of ciphers,
        characteristics of good ciphers, crypt analysis, public-key system,
        single-key system and data encryption standards, threats, safeguards and
        security objectives, security with some existing systems, security
        level. Computer virus protection, privacy and data protection,
        تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of
        Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        designing of secure system, models of security, database, security,
        reliability and integrity, sensitive data. Multi-level data, security,
        protection of files, copy protection, personal computer, security
        computer network and security. IS402: Distributed and Mobile Database
        Levels of distribution transparency. Distributed database design,
        mapping user’s transactions to distributed level. Optimization of
        accesses strategies. The management of distributed transactions.
        Distributed concurrence control, recovery in distributed database.
        Distributed database administration. Also, this course addresses the use
        of Internet databases to support Web solutions. Topics covered include
        techniques for the exchange and sorting of information, and the best way
        to achieve this through an Internet database. The emphasis is on the
        design of Internet databases that could allow the deployment of an
        entire product catalog online; dynamically-generated Web pages that
        allow visitors to share common interests on topics related to a Web
        site; a catalog linked to sites that may be useful to visitors; and,
        building a company Intranet that tracks the progress and status of
        current projects. IS403: Blockchain Technologies Discover the technology
        underpinning blockchain, understand the structure of blockchain, and
        explore the scope of the blockchain industry by analyzing the scale of
        investment in the industry, the sociopolitical and economic context, key
        stakeholders, and the evolution of the industry landscape. Learn how
        blockchain is fundamentally changing ways of doing business, and the
        impact this has on industries, consumers and society. Equipped with an
        understanding of the technology and strategies underpinning this space,
        you’ll learn how to build dynamic capabilities for innovation, and
        recommend growth paths to help you and your group scale your blockchain
        business strategy. IS404: Web and Social Media Analytics The phenomenal
        growth of social media has transformed the social, political, and
        technological landscapes. Social media sparked a revolution by putting
        knowledge production and communication tools in the hands of the masses.
        Today on sites such as Twitter, Facebook, and YouTube, large numbers of
        people publish rich content, annotate it with descriptive metadata,
        communicate and respond to others. Social media has transformed how we
        create and consume knowledge, respond to disasters, monitor environment,
        manage resources, and interact with the world and one another. What’s
        more, by exposing individual and collective behavior, social media
        delivers large quantities of social data for analysis, offering new
        research opportunities and new computational challenges. This course
        will examine topics in social data analysis, including influence and
        centrality in social media, information diffusion on networks, topic
        modeling and sentiment analysis, identifying social bots, and predicting
        behavior. We will see how AI, network analysis, and statistical methods
        can be used to study these topics. While there are no prerequisites, I
        expect students to be proficient in programming, algorithms and data
        structures, and have taken college level or above courses in linear
        algebra and statistics. AI and machine learning coursework is a plus.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 50 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        IS405: Data Mining and Machine Learning
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        Data is a critical type of business capital, and data mining is
        essential to unleash the value of data for business analytics. Mining
        data from massive amounts of data accumulated in organizations creates
        value for individuals, businesses, and society via data-driven
        decision-making or pattern based strategy. In this course, students will
        learn state-of-the-art data mining methods and theories. We will also
        discuss the applications of data mining methods to solve real-world
        business problems in a wide range of areas such as marketing, finance,
        and healthcare. The course has two objectives. First, it seeks to
        introduce you to modern data mining methods that provide useful insights
        to a large spectrum of managerial problems. Second, it aims at informing
        you the kinds of business problems that can be solved using data mining
        methods as well as how to solve these problems. IS406: Information
        Retrieval Overview of fundamental issues of information retrieval with
        theoretical foundation. Comprehensive survey of information – retrieval
        techniques and theory, covering both effectiveness and run – time
        performance of information – retrieval systems. The focus is on
        algorithms and heuristics used to find documents relevant to the user
        request and to find them fast. IS407: Data Visualization and Dashboards
        With the growing amount of data available to businesses, decision-makers
        must translate strategy into accountability, measure progress against
        goals, and leverage data for business decision making. Dashboards are
        used to present and analyzing enterprise performance data, both
        strategic and operational, and to perform business analysis easily and
        quickly. This course will teach dashboards and data visualization
        technologies, using an approach that will include theory as well as a
        significant hands-on component. Students will learn how to design and
        build dashboards, as well as create content of different types that can
        be incorporated into dashboards. This course is about data
        visualization, the art and science of turning data into readable
        graphics. We’ll explore how to design and create data visualizations
        based on data available and tasks to be achieved. This process includes
        data modeling, data processing (such as aggregation and filtering),
        mapping data attributes to graphical attributes, and strategic visual
        encoding based on known properties of visual perception as well as the
        task(s) at hand. IS408: Software Engineering This course is designed to
        provide you opportunity to gain knowledge and skills necessary to
        analysis, design and implement complex software engineering projects.
        You should learn to analysis and design complex real-life systems,
        working as teams. The project-based approach used requires you to review
        and refine your design iteratively based on regular feedback from staff.
        You are also made aware of current software engineering standards and
        processes. You are also taught to consider qualitative aspects including
        maintainability, extensibility, reusability and robustness in every
        stage of the software-engineering lifecycle. At the end of the course
        you should be able to combine top-down and bottom-up approaches to
        software design and choose
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 51 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        the most appropriate process considering the underlying technology,
        project duration, the level of risks and the customer expectations. At
        the conclusion of the course, you should be able to: Examine, identify
        and use Design Patterns in the developments of software systems;
        Understand the UML Process. IS409: Database Administration This course
        will cover disk storage, basic-file structure, hashing techniques,
        indexing structures for files, transaction processing concepts and
        theory for databases with main schedules for recoverability, schedules
        for serializability, and transaction support in SQL. Concurrency control
        techniques will be also covered such as timestamp ordering,
        multi-version concurrency control techniques, validation concurrency
        control techniques, granularity of data items and multiple granularity
        locking. The course will also cover database recovery techniques such as
        NO UNDO/REDO recovery, recovery techniques based on immediate update,
        shadow paging, and ARIES recovery algorithm. IS410: Managing Digital
        Firms This course focuses on the use of both traditional and web-based
        information technologies to manage the firm .these technologies make
        possible new business models, new organizational structures ,and new
        management processes .topics covered in new technology infrastructure
        and architecture, major functional applications of IT within the firm
        ,new IT-based business models, enterprise systems, knowledge management
        ,multinational systems ,managerial decisions about technology, and new
        organizational forms. IS411: Data Warehouses The objective of this
        course is to understand the fundamentals of data warehousing. Topics
        include basic data warehouse architecture, data consolidation, warehouse
        internals (storage and indexing materialized views and aggregate
        pre-computation), Online Analytical Processing (OLAP) systems, main
        operations of data mining, system integration issues in decision support
        tools, survey of existing mining and OLPA products, and success and
        failure stories of data mining. IS412: Business Process Management To
        meet the demands of today’s competitive marketplace enterprise processes
        must be lean and customer focused. This course looks at ways in which
        business processes can be analyzed, redesigned, and improved thus
        ensuring that they are meeting the needs of customers and the
        enterprise. A business process is a set of related activities that
        together realize a business goal in an organizational and technical
        context. These processes take place in a single organization but may
        need to interact with processes in other organizations. Business process
        management (BPM) is concerned with the concepts, methods, and techniques
        that support the design, improvement, management, configuration,
        enactment, and analysis of business processes that deliver lean and
        customer focused business processes. BPM includes process modelling that
        explicitly represents
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 52 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        processes – once they are defined, processes can be analyzed, improved,
        and enacted. Software in the form of business process management systems
        can be used to manage business process. By taking this course you will
        be able to understand business process from a management and process
        analyst perspective, learn skills, analytical frameworks and general
        principles for managing business processes. The course will incorporate
        a laboratory component using BPM software. IS413: Web-based Information
        Systems This course covers the management and development of web-based
        information systems. students will analyze, design and develop
        web-enabled database applications using several different approaches.
        Emphasis will be on concepts and architecture of new technologies.
        topics include: the CGI processing model and its alternatives ,java
        applets, java servlets, JDBC; application service providers; multitier
        client-server computing ;object-oriented models; active server pages and
        other server-based processing alternatives; distributed business objects
        such as CORBA; text processing applications (PERL, awk, etc.);and
        platform options (Windows NT vs. Unix). IS414: Business Analytics
        Programming This course is designed to introduce business analytics
        programming in Python to students. Students will learn programming
        foundations, application development in Python, and how to integrate
        Python applications with business operations in this class. This course
        consists of the following learning objectives: Students will learn
        Python programming fundamentals. Knowledge such as object-oriented
        programming in Python will be introduced to students. Students will
        learn how to use Python to perform business data analysis. Techniques
        such as time series analysis, geographical data analysis in Python will
        be introduced to students. IS415: Enterprise Information Systems This
        course focuses on the design, management, and implementation of
        IT-supported processes. The evolution of information technology and the
        near ubiquity of the internet give business firms the opportunity to
        completely redesign their business processes, to develop systems faster,
        and to implement systems in entirely new ways. topics covered in this
        course include business process analysis and design ,implementation,
        change management ,and performance measurement systems relevant
        technologies include web-based application serve providers, workflow
        management systems ,and enterprise systems .students learns how to
        analyze a business problem ,design new business processes ,and manage
        the implementation process .they also gain an understanding of the
        technology support structure required for successful implementation of
        organizational and interorganizational processes. IS416: Information
        Systems Auditing Computer forensics is the application of computer
        investigation and analysis techniques to the process of discovering and
        preserving potential legal evidence. Systems auditing is concerned with
        ensuring that adequate security controls are in place to prevent or at
        least discover fraud or other misuse of IT resources. Legal evidence
        might be sought in a wide range of computer
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 53 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        crimes or misuse and students in this course will develop an
        understanding of forensic and auditing and will develop the skills
        needed for discovering and preventing theft of trade secrets, theft of
        or destruction of intellectual property, and fraud. They will learn how
        to recover deleted, encrypted, or damaged file information and to plan
        and execute audits of security and other related IT procedures. This
        course is intended to provide a foundation in computer forensics and
        auditing and provide hands-on practice in applying forensic and auditing
        techniques. IS417: Multimedia Information Systems Recent advances in
        digital media technology and rapid growth of social media platforms
        where multimedia objects such as images, videos and music (audio), and
        mobile and geospatial data, are increasingly embedded in online social
        communities. As a result, multimedia has gained enormous potential in
        improving the traditional educational, professional, business,
        communication and entertainment processes. To be able to use this
        potential for transferring these processes into user-friendly multimedia
        applications, technology is required that can help us access, deliver,
        browse, search, enrich and share multimedia content. This course covers
        such topics as organizing multimedia content, physical storage and
        retrieval of multimedia data, Content-based Search and retrieval,
        creating and delivering networked and multimedia presentations, securing
        multimedia content and current research directions in this area. The
        main objective of the course is to introduce students’ different types
        of multimedia data, different techniques to store, manipulate, and
        retrieve multimedia data residing across global computer networks.
        IS418: Software Requirements Analysis Students will learn how to capture
        software requirements and handle difficult situations in gathering data
        to build systems. Special emphasis is given to working with clients and
        to learning about the needs of users who interact with a system. The
        course addresses elicitation, specification, and management of software
        system requirements. Additionally, the course examines iterative
        prototyping user interactions for a system. IS419: Systems Design
        Patterns This course extends object-oriented analysis and design by
        incorporating design patterns to create interactive applications.
        Through a survey of established design patterns, you will gain a
        foundation for more complex software applications. Finally, you will
        identify problematic software designs by referencing a catalog of code
        smells. IS420: Knowledge Management The focus of this course is a blend
        of theories, approaches and technologies for managerial problem solving
        and knowledge management. The course reviews common fallacies and
        pitfalls in decision making and seeks to equip students with the
        knowledge of managerial techniques and information technologies for
        effective organizational decision making. Students will be exposed to
        methods and technologies for leveraging intellectual capital, both at an
        individual and firm level. Major topics of the course include "decision
        traps", problems in dynamic decision making,
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 54 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 55
        نم 74 system thinking, decision support, and technologies that
        facilitate knowledge sharing, knowledge management and organizational
        learning. IS421: Cloud Databases Extend your core DBA skills to the
        cloud with Database Cloud Services training. Learn how to migrate your
        Database to the Cloud and use Database Cloud Services. Learn how to
        create database applications in the cloud. Learn the key areas of
        functionality of the Oracle Database as a Service (DBaaS) implementation
        with an Oracle Database Cloud certification. Discover how to use these
        key areas to develop, deploy, administer, and tune DbaaS. IS422:
        Information Systems Quality Assurance The course takes its starting
        point in well-established research around information systems quality
        and highlights three subareas; Information Quality, Software Quality and
        Business Value. This course is about the theory, models and practice of
        software testing and quality assurance. The subject matter focuses on
        three broad areas: (1) Theory of Software Testing that reviews the
        relevant techniques and research results, the aim is to provide the
        student with a solid foundation from which to build real-world testing
        systems and teams. (2) Testing in practice that looks at the process and
        practice of testing, including the role of tester in an iterative,
        incremental development project. (3) Test automation is essential for
        modern software testing. Several automation methods are discussed and a
        survey of tools, both commercially available ones and homegrown is
        performed. The course will mostly be based on research papers and the
        latest publications in the community of quality assurance. IS425:
        Selected Topics in Information Systems I Selected Topics provides an
        opportunity to study a topic which is not included in the existing
        curriculum. This course examines one or more selected current issues in
        the area of Information Systems. Topics chosen for study will be by
        arrangement with the department. IS426: Selected Topics in Information
        Systems II Selected Topics provides an opportunity to study a topic
        which is not included in the existing curriculum. This course examines
        one or more selected current issues in the area of Information Systems.
        Topics chosen for study will be by arrangement with the department.
        IS430/IS431: Project This course will continue for two semesters. In the
        first semester, a group of students will select one of the projects
        proposed by the department and analyze the underlying problem. In the
        second semester, the design and implementation of the project will be
        conducted. The student will deliver oral presentations, progress
        reports, and a final report. GI301: Spatial Database The goal of this
        course is to introduce the main features of spatial databases, the
        kernel of Geographic Information Systems (GIS). Topics include spatial
        concepts and data models, spatial ةعماج قيزاقزلا– لك ةي تابساحلا
        تامولعملاو Zagazig University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 56
        نم 74 query languages, spatial storage and indexing, query processing
        and optimization, spatial networks, introduction to spatial data mining.
        Exercises and practical work will be concentrated on building and
        designing geodatabases. GI302: Principles of Remote Sensing This course
        is designed to provide students with the basic knowledge of biophysical,
        quantitative, and digital remote sensing. Both the theoretical basis and
        practical aspects of these approaches to remote sensing are addressed.
        Topics examine include remote sensing applications in natural
        environment such as meteorology, oceanography, hydrology, and biomass
        detection. GI400: Digital Cartography and Visualization An overview of
        the development of Cartography, the concepts, processes, techniques and
        data sources. The role of Cartography in digital mapping and Geographic
        Information Systems. Rules of graphical communication and the depiction
        of spatial data. The Cartographic process: need, data sources,
        evaluation, scale, reference base, projection, design specifications,
        compilation, production and final output. Graphical elements of design
        and symbolization. Applications of the representation of spatially
        referenced data in the areas of sociological, economical, topographical
        and environmental the traditional and digital approaches to cartographic
        design, production methods and user/supplier requirements. Evaluation of
        the cartographic processes for applicability. The functionality of
        digital mapping programs and the cartographic software of Geographical
        Information Systems. The cognitive processes of spatial data capture and
        the present methods of data visualization. Knowledge based map design
        techniques. Multimedia and virtual reality as visualization techniques.
        GI401: Spatial Analysis and Modeling This course explores methods of
        analyzing spatial data in the interactive and graphical environment of a
        GIS. The course draws on related theory in spatial statistics,
        geo-statistics, geographical analysis and cartographic modeling to
        provide a set of generic techniques for GIS users. Topics include the
        analysis of point patterns, networks, overlay analysis, spatial
        interaction models, and visualization of spatial data (virtual reality,
        simulation of landscape, animation, multi-media). The course concludes
        by considering how to extend the spatial analytical capabilities of GIS
        and points to the evolution of spatial decision support systems.
        Associated exercises and hands-on allow methods to be applied in a GIS
        context. GI402: GIS Programming This course introduces the development
        of GIS applications. During this course students will learn to use
        programming techniques to create applications that perform fundamental
        spatial analysis and automation tasks, such as geoprocessing, editing,
        database management, projecting data, and map creation. GI403: Spatial
        Decision Support Systems This course introduces students to key
        theories, concepts and techniques that have been ةعماج قيزاقزلا– لك ةي
        تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 57
        نم 74 developed recently to improve the decision support capabilities of
        spatial information systems. Topics covered include participatory GIS,
        group-based spatial decision support systems, and the integration of
        multi-criteria analysis (MCA) methods with GIS to facilitate decision
        making in planning. GI404: Web and Mobile GIS This course discusses the
        design and implementation of locally served and cloud-based geospatial
        web applications. Construction of web maps, mashups and volunteered
        geographic information interfaces. This course will also discuss design,
        coding and implementation of mobile GIS applications using the Java and
        JavaScript object-oriented programming languages. GI405: Spatial
        Statistics This course reviews some popular approaches to statistically
        analyze data in their spatial context, providing hands-on experience
        with widespread software. The module starts with a review of basic
        statistical concepts and their extension by adding the spatial aspect;
        it then explains concepts and methods of pattern analysis, introduces
        methods for measuring spatial dependence, and finally provides a compact
        gateway to geostatistics. GI406: Open and Distributed GIS
        Infrastructures Currently, all over the globe and at all scale levels,
        Spatial Data Infrastructure projects can be recognized. Their aim is to
        improve the availability of and access to geospatial data. With the
        paradigm shift “from systems to services”, Spatial Data Infrastructures,
        spatial data marketplace and geo business have become keywords in the
        GIS world. A common understanding of these concepts is given which helps
        to evaluate the political and economic impact of distributed geo
        processing and the OGC process. The technological side to these
        developments such as WMS as the first Web Service standard of the OGC,
        and XML and GML are introduced. GI407: Medical GIS Explore how spatial
        data and geographic information systems (GIS) can be used to understand
        and improve public health. The environment in which we live, and work
        can have a profound effect on our health – an effect that is explored by
        the emerging field of geohealth. This course will introduce you to new
        developments in geohealth, looking at the latest thinking and methods
        for using spatial data and geographic information systems (GIS) in
        health settings. GI408: GIS Network Modeling Examines the theory of
        network analysis and its application in Geographic Information Systems.
        Topics covered include graph theoretic measures of network connectivity
        and proofs of network properties; optimization problems including
        shortest path algorithms, flow algorithms, and assignment problems on
        networks; special solution procedures for the classic transportation
        problem; procedures for linear referencing and urban travel demand
        modeling. The implementation of these algorithms and procedures with GIS
        data structures is explored using industry standard GIS software. ةعماج
        قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of
        Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 58
        نم 74 GI409: Spatial Data Mining Spatial data mining is the branch of
        data mining that deals with spatial data. This course focuses on
        algorithm techniques that can be used for spatial data mining tasks such
        as classification, association rule mining, clustering, and numerical
        prediction. This includes probabilistic and statistical methods, genetic
        algorithms and neural networks, visualization techniques, and
        mathematical programming. We also place such data mining within the
        larger picture of knowledge discovery in databases and its relationship
        with data warehousing. We will consider numerous case studies from
        different application areas such as remote sensing, ecology, weather,
        natural disasters, public health, transportation, and criminal analysis.
        GI410: Applied Remote Sensing Focuses on the application of remote
        sensing techniques to solving real world urban and environmental
        problems in areas such as urban and suburban landscape, lane use and
        land cover, transportation and communication, vegetation and forestry,
        biodiversity and ecology, water and water quality control, soils and
        minerals, geology and geomorphology studies. The current generation,
        industry standard software is used for labs and applications
        development. GI411: Spatial Big Data This course will introduce basic
        concepts and techniques related to Spatial Big Data from a computational
        perspective. Topics to be covered include: Introduction to Spatial Big
        Data Analytic, Types of Spatial Big Data, Spatial Statistic Foundation,
        Spatial Colocation Discovery, Spatial Outlier Analysis, Spatial
        Prediction, Spatial Hotspot, Spatial Summarization, Spatial and
        Spatiotemporal Change, Spatial Big Data Platform, Spatiotemporal Big
        Data, and Recent Trends. GI412: GIS and Cyber Security This course
        introduces technical aspects of cyber security in geographic information
        systems. It describes threats and types of attacks against geographic
        information systems to enable students to understand and analyze
        security requirements and define security policies. Security mechanisms
        and enforcement issues will be introduced. GI413: Cloud GIS This course
        teaches students to use cloud and server GIS resources to solve problems
        for which geospatial data is an integral element. We will evaluate and
        implement systems using three cloud service models (infrastructure
        services, platform services, and software services). The course will
        teach students to set up cloud services for creating maps, cloud
        services for managing spatial data, and cloud services for processing
        spatial data. GI414: Spatial Data Acquisition Techniques This course
        introduces surveying and mapping techniques of use to GIS professionals,
        including the Global Positioning System (GPS). Topics include: basic
        traditional survey methods, including horizontal and vertical location
        techniques; geodesy; data adjustments; datum and تامولعملاو تابساحلا لك
        قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers and
        Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ellipsoids; coordinate systems; and transformations; understand the
        issues surrounding data quality; learn the difference between terms such
        as precision, absolute accuracy, relative accuracy, classification
        accuracy, temporal accuracy, and thematic accuracy. GI425: Selected
        Topics in Geoinformatics I Selected Topics provides an opportunity to
        study a topic which is not included in the existing curriculum. This
        course examines one or more selected current issues in the area of
        Spatial informatics. Topics chosen for study will be by arrangement with
        the department. GI426: Selected Topics in Geoinformatics II Selected
        Topics provides an opportunity to study a topic which is not included in
        the existing curriculum. This course examines one or more selected
        current issues in the area of Spatial informatics. Topics chosen for
        study will be by arrangement with the department. GI430/GI431: Project
        This course will continue for two semesters. In the first semester, a
        group of students will select one of the projects proposed by the
        department and analyze the underlying problem. In the second semester,
        the design and implementation of the project will be conducted. The
        student will deliver oral presentations, progress reports, and a final
        report.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 59 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 60
        نم 74 اعبار تاررقمل يملعلا ىوتحملا : امولعملا ايجولونكت مسق ت IT100:
        Digital Logic Design This course provides a modern introduction to logic
        design and the basic building blocks used in digital systems, digital
        computers. It starts with a discussion of combinational logic: logic
        gates, minimization techniques, arithmetic circuits, and modern logic
        devices such as field programmable logic gates. The second part of the
        course deals with sequential circuits: flip flops, synthesis of
        sequential circuits, and case studies, including counters, registers,
        and random-access memories. State machines will then be discussed and
        illustrated through case studies of more complex systems using
        programmable logic devices. Different representations including truth
        table, logic gate, timing diagram, switch representation, and state
        diagram will be discussed. The course has an accompanying lab component
        that integrates hands-on experience with modern computer-aided design
        software including logic simulation, minimization and an introduction of
        the use of hardware description language (VHDL). IT201: Computer
        Graphics Computer Graphics is a study of the hardware and software
        principles of interactive raster graphics. Topics include an
        introduction to the basic concepts, 2-D and 3-D modeling and
        transformations, viewing transformations, projections, rendering
        techniques, graphical software packages and graphics systems. Students
        will use a standard computer graphics API to reinforce concepts and
        study fundamental computer graphics algorithms. This course examines one
        or more selected current issues in the area of image synthesis. Specific
        topics covered are dependent on the instructor. Potential topics include
        scientific visualization, computational geometry, photo-realistic image
        rendering and computer animation. IT200: Computer Organization and
        Architecture An introduction to computer architecture. Includes a survey
        of computer architecture fundamentals exemplified in commercially
        available computer systems, including classical CPU and control unit
        design, register organization, primary memory organization and access,
        internal and external bus structures, and virtual memory schemes.
        Alternatives to classical machine architecture, such as the stack
        machine and the associative processor, are defined and compared.
        Parallel processors and distributed systems are also presented, along
        with an analysis of their performance relative to nonparallel machines.
        IT203: Multimedia This course Applies basic knowledge of mathematics and
        science in multimedia system computing introduce students to the
        different media types and design issues related to multimedia systems;
        The course examines types of multimedia information: voice, data video
        facsimile, graphics, and their characterization; modeling techniques to
        represent multimedia information; introduce students to the advances in
        multimedia compression technology; Provide an opportunity for students
        to apply design, implementation and evaluation concepts and techniques
        to the development of a small but realistic multimedia system. Define
        criteria and تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University
        – Faculty of Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        specifications appropriate to multimedia compression systems problems,
        and plan strategies for their solution. Identify the current and
        underlying technologies that support multimedia compression processing.
        IT202: Computer Networks The principles and practice of computer
        networking, with emphasis on the Internet. The structure and components
        of computer networks, packet switching, layered architectures, OSI
        7-layer model, TCP/IP, physical layer, error control, window flow
        control, local area networks (Ethernet, Token Ring; FDDI), network
        layer, congestion control, quality of service, multicast. IT301: Web and
        Network Programming This course aims to give a broad knowledge of modern
        networking technologies and network based applications, computing
        systems, and software. The course will cover the background and history,
        basic concepts and components, mechanisms and protocols of computer
        networks and Internet. The scope will extend to the World Wide Web
        computing and information exchange framework built on top of Internet
        and introduce key technologies that enable the client–server web
        application modes. You are expected to finish the course with necessary
        knowledge and understanding of the rationale in modern computer
        networking and network centric system and application design. IT302:
        Introduction to Internet of Things (IoT) The Internet of Things (IoT) is
        everywhere. It provides advanced data collection, connectivity, and
        analysis of information collected by computers everywhere—taking the
        concepts of Machine-to-Machine communication farther than ever before.
        This course gives a foundation in the Internet of Things, including the
        components, tools, and analysis by teaching the concepts behind the IoT
        and a look at real-world solutions. IT303: Introduction to Cloud
        Computing This course provides a hands-on comprehensive study of Cloud
        concepts and capabilities across the various Cloud service models
        including Infrastructure as a Service (IaaS), Platform as a Service
        (PaaS), Software as a Service (SaaS), and Business Process as a Service
        (BPaaS). IaaS topics start with a detailed study the evolution of
        infrastructure migration approaches from VMWare/Xen/KVM virtualization,
        to adaptive virtualization, and Cloud Computing / on demand resources
        provisioning. Mainstream Cloud infrastructure services and related
        vendor solutions are also covered in detail. PaaS topics cover a broad
        range of Cloud vendor platforms including AWS, Google App Engine,
        Microsoft Azure, Eucalyptus, OpenStack and others as well as a detailed
        study of related platform services such as storage services that
        leverage Google Storage, Amazon S3, Amazon Dynamo, or other services
        meant to provide Cloud resources management and monitoring capabilities.
        The SaaS and PaaS topics covered in the course will familiarize students
        with the use of vendor-maintained applications and processes available
        on the Cloud on a metered on-demand basis in multi-tenant environments.
        The course also covers the Cloud security model and associated
        challenges and delves into the implementation and
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 61 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 62
        نم 74 support of High-Performance Computing and Big Data support
        capabilities on the Cloud. Through hands-on assignments and projects,
        students will learn how to configure and program IaaS services. They
        will also learn how to develop Cloud-based software applications on top
        of various Cloud platforms, how to integrate application-level services
        built on heterogeneous Cloud platforms, and how to leverage SaaS and
        BPaaS solutions to build comprehensive end-to end business solutions on
        the Cloud. IT300: Digital Signal Processing This course will cover :
        Digital processing of signals, sampling, difference equations, discrete
        time Fourier transforms, discrete and fast Fourier transforms, digital
        filter design, LTI systems, Z-transform, Multirate signal processing,
        Filter Banks, Wavelets and Applications to mp3 and JPEG , Overview of
        FIR and IIR filter design techniques, DFT, FFT, and role of DCT in MPEG
        and JPEG, and Spectral Analysis. IT304: Introduction to Cybersecurity
        This is an introductory course designed to familiarize students with the
        concepts of cybersecurity. The course will prepare students for
        succeeding courses in cybersecurity and forensics. Course Topics:
        Introduction to Information Security, The Need for Security, Legal,
        Ethical, and Professional Issues in Information Security, Risk
        Management, Planning for Security, Security Technology: Firewalls, VPNs,
        and Wireless, Security Technology: Intrusion Detection and Prevention
        Systems and Other Security Tools, Cryptography, Physical Security,
        Implementing Information Security, Security and Personnel, Information
        Security Maintenance and eDiscovery. IT305: Mobile Application
        Development There are more mobile devices on the planet than people.
        Mobile app development helps to unleash the full power of mobile devices
        and push their usage into every corner of modern society. This course
        introduces students to important concepts and aspects in mobile
        application development on Java based Android phones, including UI
        design, data persistence, multimedia support, sensor management,
        multithreading, debug and test, and application publishing. Although the
        course is centered on Android, general principles of mobile app
        development discussed here can also be applied to other contexts. IT306:
        Wireless and Mobile Networks This course provides a comprehensive
        treatment of wireless data and telecommunication networks. Topics
        include recent trends in wireless and mobile networking, wireless coding
        and modulation, wireless signal propagation, IEEE 802.11a/b/g/n/ac
        wireless local area networks, 60 GHz millimeter wave gigabit wireless
        networks, vehicular wireless networks, white spaces, IEEE 802.22
        regional area networks, Bluetooth and Bluetooth Smart, wireless personal
        area networks, wireless protocols for Internet of Things, ZigBee,
        cellular networks: 1G/2G/3G, LTE, LTE-Advanced, and 5G. تامولعملاو
        تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of Computers
        and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        IT307: Embedded Systems Design ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        Embedded systems are the systems of future with cellular phones,
        smart-phones, tablets becoming the dominant platforms for computing and
        communication. This course introduces fundamental knowledge of Embedded
        systems. The ubiquity of information and the associated need for the
        computation that accompanies it is driving this revolution only to be
        accelerated by the new paradigms such as the Internet-of-Things (IoT).
        These platforms are clearly very different in terms of their processing
        requirements which are unique: real-time needs, high performance but at
        low energy, compact-code and data segments, and most importantly ever
        changing software stack. Such unique requirements have led to a complete
        redesign and reinvention of the both hardware and the software stack
        from ground up, for example, brand new processors such as ARM, DSPs,
        network processors were invented all the way up to new virtual machines
        such as Dalvik. IT308: Microcontrollers This course provides the main
        principles of microprocessors and microcontrollers and their
        peripherals. Various related topics are covered including introduction
        to computing system; microcontroller architecture; instruction set;
        assembly language programming; hardware interfaces; memory management.
        IT401: Image Processing This course will provide students with a
        preliminary understanding of the theory and practice of image
        processing. Basic concepts and fundamentals of image processing and
        analysis will be described in the course. The spatial frequency domain
        (The sampling theorem, template matching and the convolution theorem,
        spatial filtering). Enhancement and restoration, image segmentation.
        Image representation: (Spatial differentiation and smoothing, template
        matching, region analysis, contour following). IT402: Network Security
        Discussion of the need for network security, describe various threats,
        attack types and hackers. Explain authentication, encryption &
        encryption standard. Secret-Key, public key algorithm authentication
        protocols, digital certificate. Virtual private network, (VPN), secure
        sockets layer (SSL). Firewalls, and firewalls topology, packet filters
        and proxy servers. Threats and couther measures in centralized and
        distributed systems; communication security techniques based on
        encryption; symmetric and asymmetric encryption; encryption modes,
        including stream and block encryption, and cipher-block chaining;
        message origin and mutual authentication; third party and inter-realm
        authentication; authentication of mobile users; data confidentiality and
        integrity protocols; formal analysis of authentication protocols and
        message integrity; access control in distributed systems and networks;
        firewall design. IT403: Computer Vision This course introduces computer
        vision, including fundamentals of image formation, camera imaging
        geometry, feature detection and matching, stereo, motion estimation and
        tracking, image
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 63 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 64
        نم 74 classification, scene understanding, and deep learning with neural
        networks. We will develop basic methods for applications that include
        finding known models in images, depth recovery from stereo, camera
        calibration, image stabilization, automated alignment, tracking,
        boundary detection, and recognition. IT404: Robotics Introduction to
        Robotics; Co-ordinate systems(Cartesian, cylindrical ; Polar and
        Revolute systems); Robot Arms(Axes, ranges , Off-set and In-line Wrist,
        Roll, Pitch and Yaw); End Effectors; Sensors (Micro-switches, Resistance
        Transducers, Peizo-electric, infrared , Laser and Vidicon Tubes);
        Application of sensors (Reed Switches, Ultra Sonic, Bar Code Readers ) ;
        Hydraulic system units (pumps, valves, solenoids, cylinders) ;
        Electrical system units ( stepper motors, encoders and AC motors);
        programming of Robots ; Safety considerations. IT405: Computer Animation
        and 3D Modeling Kinematics and techniques for character animation.
        Topics include physical modeling and simulation, motion planning,
        control and learning algorithms, locomotion, motion trajectory
        optimization, scripting languages, motion capture and motion editing.
        Students will implement algorithms and interactive animation tools.
        IT406: Information Theory and Data Compression This course is about how
        to measure, represent, and communicate information effectively. Why bits
        have become the universal currency for information exchange. How
        information theory bears on the design and operation of modern-day
        systems such as smartphones and the Internet. What are entropy and
        mutual information, and why are they so fundamental to data
        representation, communication, and inference. Practical compression and
        error correction. Relations and applications to probability, statistics,
        machine learning, biological and artificial neural networks, genomics,
        quantum information, and blockchains. IT407: Virtual and Augmented
        Reality Design and implementation of software systems necessary to
        create virtual environments; techniques for achieving real time, dynamic
        display of photorealistic, synthetic images; hands-on experience with
        electromagnetically tracked, head mounted displays. Final project
        requires the design and construction of a virtual environment. IT408:
        Network Simulation The course is intended to provide the participants
        knowledge in modeling and simulation of telecommunication networks. The
        course features software commonly used for telecommunication network
        simulation. In addition to the basic theory necessary for understanding
        network simulation, the course aims to give practical skills in using
        these tools to design and implement simulation models for performance
        analysis of wired as well as wireless networks. After completing the
        course, the students should be able to: Describe the main quantitative
        methods for performance evaluation of telecommunication networks;
        Explain the ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University
        – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 65
        نم 74 advantages and drawbacks of using simulation as a tool for
        analyzing telecommunication networks; Describe common assumptions,
        simplifications, and generalizations made in modeling telecommunication
        systems; Implement, verify and validate simulation models of
        telecommunication networks; Design, build, and experiment with
        simulation models, as well as evaluate the results obtained by
        simulation IT409: Computer Forensics Introduces computer security
        administrators to computer forensics. Includes setup and use of an
        investigator's laboratory, computer investigations using digital
        evidence controls, processing crime and incident scenes, performing data
        acquisition, computer forensic analysis, e-mail investigations, image
        file recovery, investigative report writing, and expert witness
        testimony. IT410: Cloud Networks In the cloud networking course, we will
        see what the network needs to do to enable cloud computing. We will
        explore current practice by talking to leading industry experts, as well
        as looking into interesting new research that might shape the cloud
        network’s future. This course will allow us to explore in-depth the
        challenges for cloud networking—how do we build a network infrastructure
        that provides the agility to deploy virtual networks on a shared
        infrastructure, that enables both efficient transfer of big data and low
        latency communication, and that enables applications to be federated
        across countries and continents? Examining how these objectives are met
        will set the stage for the rest of the course. This course places an
        emphasis on both operations and design rationale. IT4011: Pattern
        Recognition Pattern recognition techniques are used to design automated
        systems that improve their own performance through experience. This
        course covers the methodologies, technologies, and algorithms of
        statistical pattern recognition from a variety of perspectives. Topics
        including Bayesian Decision Theory, Estimation Theory, Linear
        Discrimination Functions, Nonparametric Techniques, Support Vector
        Machines, Neural Networks, Decision Trees, and Clustering Algorithms
        etc. will be presented. IT412: Multimedia Mining Multimedia mining deals
        with the extraction of implicit knowledge, multimedia data
        relationships, or other patterns not explicitly stored in multimedia
        files. Multimedia mining is more than just an extension of data mining,
        as it is an interdisciplinary endeavor that draws upon expertise in
        computer vision, multimedia processing, multimedia retrieval, data
        mining, machine learning, database and artificial intelligence. IT413:
        Optical Networks Principles and procedures of optical networking with
        focus on high speed optical signal transmission between network nodes,
        light path routing and distribution, multilayer network ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 66
        نم 74 design, and advanced photonic techniques and devices for optical
        signal transmission and switching. IT414: Quantum Computing Quantum
        computing exploits the quantum mechanical nature of matter to
        simultaneously exist in multiple possible states. Building up on the
        digital binary logic of bits, quantum computing is built based on
        interacting two-level quantum systems or ‘qubits’ that follow the laws
        of quantum mechanics. Addressability of the quantum system and its
        fragility to fidelity are the major issues of concern, which if
        addressed appropriately, will enable this new approach to revolutionize
        the present form of computing. After developing the basics, this course
        delves on various implementation aspects of quantum computing and
        quantum information processing. IT415: Computational Imaging
        Computational imaging systems have a wide range of applications in
        consumer electronics, scientific imaging, HCI, medical imaging,
        microscopy, and remote sensing. We discuss light fields, time-of-flight
        cameras, multispectral imaging, thermal IR, computational microscopy,
        compressive imaging, computed tomography, computational light transport,
        compressive displays, phase space, and other topics at the convergence
        of applied mathematics, optics, and high-performance computing related
        to imaging. Hands-on assignments. IT416: Game Development This course
        furthers the students understanding of programming through the
        implementation of advanced programming methods employed in video game
        production. Various data structures used to represent, organize and
        manage game world information will be explored and implemented in gaming
        solutions. IT417: Wireless Sensors Networks This course deals with the
        comprehensive knowledge about wireless sensor networks. It provides an
        insight into different layers and their design considerations. A
        thorough knowledge of infrastructure establishment and sensor network
        platform is provided. IT425: Selected Topics in Information Technology I
        Selected Topics provides an opportunity to study a topic which is not
        included in the existing curriculum. This course examines one or more
        selected current issues in the area of Information Technology. Topics
        chosen for study will be by arrangement with the department. IT426:
        Selected Topics in Information Technology II Selected Topics provides an
        opportunity to study a topic which is not included in the existing
        curriculum. This course examines one or more selected current issues in
        the area of Information Technology. Topics chosen for study will be by
        arrangement with the department. تامولعملاو تابساحلا لك قيزاقزلا ةي
        ةعماج Zagazig University – Faculty of Computers and Informatics ـــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        IT430/IT431: Project ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        This course will continue for two semesters. In the first semester, a
        group of students will select one of the projects proposed by the
        department and analyze the underlying problem. In the second semester,
        the design and implementation of the project will be conducted. The
        student will deliver oral presentations, progress reports, and a final
        report.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 67 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 68
        نم 74 اسماخ تاررقمل يملعلا ىوتحملا : رارقلا معدو تايلمعلا ثوحب DS100:
        Operations Research This course is an introduction to the use of
        quantitative methods in business decision-making. Topics include linear
        programming, decision making under certainty, forecasting, queuing, and
        inventory systems. DS300: Software Project Management This course
        introduces the fundamental principles of project management from an
        information technology (IT) perspective. Critical features of core
        project management are covered including integration management, scope
        management, time management, cost management, quality management, human
        resource management, communication management, risk management, and
        procurement management. Also covered is information technology
        management related to project management: user requirements management,
        infrastructure management, conversion management, software
        configuration, workflow management, security management, interface
        management, test management, customer management, and support
        management. The following areas of change management related to project
        management will also be covered: realization management, sponsorship
        management, transformation management, training management, and
        optimization management. Students will explore and learn hands-on skills
        with project management software assignments and participate in a health
        care systems implementation course-long group project intended to apply
        these newly developed knowledge and skills in a controlled environment.
        DS301: Modeling and Simulation Basic simulation modeling, nature of
        simulation. system models & simulation, discrete event simulation,
        simulation of a single-server queuing system, simulation of an inventory
        system, list processing in simulation, simulation languages, simulation
        of time sharing systems, simulation output data and stochastic
        processes, building valid and credible simulation models, principles of
        valid simulation modeling, verification of simulation computer programs,
        an approach for developing valid & credible simulation models,
        statistical procedures for computing real-world observation & simulation
        output data, some practical considerations: selecting input probability
        distributions, random number generators, generating random variables,
        output data analysis for a single system. DS302: Advanced operations
        Research This course is an introduction to nonlinear programming
        problems, unconstrained optimization search techniques. Kuhn-Tucker
        theorems, quadratic programming, separable programming, meta heuristics,
        goal programming, and dynamic programming. DS303: Decision Support
        Methodologies This course provides an overview of current trends in
        Decision support systems; This mainly includes DSS methodology and
        covers Rapid DSS application, Management support systems ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 69
        نم 74 and other techniques. Understand how management uses computer
        technologies. Learn basic concepts of decision-making, Understands
        decision support systems. Recognize different types of decision support
        systems used in the workplace. DS304: Quality Management Total quality
        management (TQM) is a philosophy, methodology and system of tools aimed
        to create and maintain mechanism of organization’s continuous
        improvement. It involves all departments and employees into improvement
        of processes and products. It helps to reduce costs and to meet and
        exceed needs and expectations of customers and other stakeholders of an
        organization. TQM encompasses the concepts of business and social
        excellence that is sustainable approach to organization’s competition,
        efficiency improvement, leadership and partnership. The objectives of
        this course are to introduce the main principles of business and social
        excellence, to generate knowledge and skills of students to use models
        and quality management methodology for the implementation of total
        quality management in any sphere of business and public sector. DS305:
        Systems Dynamics Modeling The goal of this course is to provide students
        with an introduction to the field of system dynamics computer simulation
        modeling. The course begins with the history of system dynamics and the
        study of why policy makers can benefit from its use. Next, students
        systematically examine the various types of dynamic behavior that
        socioeconomic systems exhibit and learn to identify and model the
        underlying nonlinear stock-flow-feedback loop structures that cause
        them. The course concludes with an examination of a set of well-known
        system dynamics models that have been created to address a variety of
        socioeconomic problems. Emphasis is placed on how the system dynamics
        modeling process is used to test proposed policy changes and how the
        implementation of model-based results can improve the behavior of
        socioeconomic systems. DS400: Data Analytics Data Analytics is the
        science of analyzing data to convert information to useful knowledge.
        This knowledge could help us understand our world better, and in many
        contexts enable us to make better decisions. While this is broad and
        grand objective, the last 20 years has seen steeply decreasing costs to
        gather, store, and process data, creating an even stronger motivation
        for the use of empirical approaches to problem solving. This course
        seeks to present you with a wide range of data analytic techniques and
        is structured around the broad contours of the different types of data
        analytics, namely, descriptive, inferential, predictive, and
        prescriptive analytics. DS401: Stochastic Methods. Stochastic processes
        and other applications of probability theory. Use of spreadsheet and
        other software tools for analysis, simulation and decision theory.
        Models for business operations and planning, computer systems,
        transportation, finance. تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج
        Zagazig University – Faculty of Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        DS402: Strategic Management and Business Analysis
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        This course provides a comprehensive analysis of individual and group
        behavior in organizations. Its purpose is to provide an understanding of
        how organizations can be managed more effectively and at the same time
        enhance the quality of employees work life. Topics include motivation,
        rewarding behavior, stress, individual and group behavior, conflict,
        power and politics, leadership, job design, organizational structure,
        basic concept of strategic management, Strategic management process,
        Environmental scanning and industry analysis, Corporate governance: role
        of the board of directors , Strategic management process Strategy
        formulation: situation analysis and business, Strategy implementation:
        organizing for action, decision-making, communication and organizational
        change and development DS403: Computational Intelligence This course
        aims to develop a deeper understanding of optimal decision-making
        models, algorithms and applications to engineering, decision sciences,
        and machine learning. To provide an insight for algorithm design and
        formulation of decision models. DS404: Supply Chain Planning and
        Logistics. Covers concepts in designing, analyzing, improving, measuring
        and controlling logistics operations in modern supply chains. Students
        are presented with logistics concepts, techniques, planning tools, and
        case studies to facilitate learning. Provides an overview of how
        enterprise business systems operate and are used to manage operations
        and supply chains in order to make effective business decisions. DS405:
        Forecasting Techniques This course includes techniques of preparing
        sales and financial forecasts, estimate the relative error in these
        forecasts, hands-on approach, and transform data and information into a
        competitive advantage, identify major trends in cash budgeting and cash
        flow planning, understand forecasting error and the impact of
        uncertainty participants. Software packages correlated to advanced
        forecasting techniques are used. DS406: Service Management This case
        course explores the dimensions of successful service firms. It prepares
        students for enlightened management and suggests creative
        entrepreneurial opportunities. Outstanding service organizations are
        managed differently than their "merely good" competitors. Actions are
        based on totally different assumptions about the way success is
        achieved. The results show not only in terms of conventional measures of
        performance but also in the enthusiasm of the employees and quality of
        customer satisfaction. Beginning with the service encounter, service
        managers must blend marketing, technology, people, and information to
        achieve a distinctive competitive advantage. This case will study
        service management from an integrated viewpoint with a focus on customer
        satisfaction. The material will integrate operations, marketing,
        strategy, information technology and organizational issues. Finally,
        because the service sector is
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 70 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74 ةعماج قيزاقزلا–
        لك ةي تابساحلا تامولعملاو Zagazig University – Faculty of Computers and
        Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 71
        نم 74 the fastest-growing sector of the economy, this course is intended
        to help students discover entrepreneurial opportunities. DS407:
        Operations Management This course offers practical guidance about
        application of management sciences to the planning and design of
        production, distribution, and service systems. Case studies of real
        world industrial and financial applications DS408: Multi-Objective
        Programing This course will focus on methods for multi-objective
        optimization and their combination with multi-criteria decision-making
        techniques. First, classical methods for treating multi-objective
        problems will be presented, and their deficiencies will be clarified.
        Next, advanced methods, which are based on Pareto-optimality, will be
        presented. The major part of the course will be based on evolutionary
        techniques for optimization problems with and without constraints.
        Leading algorithms will be presented and compared, such as: NSGA-II,
        SPEA-2, and MO-CMA ES. Methods to compare algorithms will be detailed
        including test functions and measures to analyze the obtained
        approximated Pareto-optimal set and front. The numerical limitations of
        the presented algorithms will be clarified. Methods to cope with such
        limitations will be described and how to handle the course of
        dimensionality of the Pareto-front. Finally, multi-criteria
        decision-making approaches will be presented, and their combination with
        multi-objective optimization will be described. A focus will be given on
        demonstrations from different application areas such as: mechanical
        design, robotics and control. Students will be allowed to make a
        programming project in one of these areas or in other areas such as:
        machine learning, aeronautical design, environmental engineering,
        industrial engineering, and electrical engineering. DS409: Production
        Planning and Inventory Control. This course is an introduction to
        Principles of production planning, master scheduling, job sequencing,
        design and control of deterministic and stochastic inventory systems,
        material requirement planning DS409: Statistical Software for decision
        science Applications. Hands-on experience with statistical software
        commonly used in industry. Data preparation, advanced statistical
        methods for business problems - marketing, finance, operations, etc.
        Interpretation and communication of results to guide decision making.
        DS410: Decision and Game Theory This course includes basic concepts of
        decision making under certainty, risk and uncertainty, The use of
        decision tables, decision trees and sequential decision-making,
        opportunity loss, one-time decisions and expected value of information,
        multiple comparison and multiple ranking methods, examining the many
        facets of game theory, such as bargaining theory, non-cooperative games,
        ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig University – Faculty
        of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 72
        نم 74 cooperative games, games with incomplete information, several
        cases studies will be used to illustrate the application of decision
        theory to real world problems. DS411: Decision and Risk Management This
        course is an introduction to risk management. Loan and credit
        management; credit scoring. Risk measurements and reserves; banking and
        insurance capital requirements, the BASEL accord, tail events and
        catastrophic event insurance. Financial contracts and hedging. Overview
        of enterprise-wide risk management strategies and techniques: strategies
        that firms employ to enhance value and minimize exposure; techniques
        used to identify, measure, reduce, and transfer risk. DS412:
        Optimization Models and Methods This course introduces basic methods of
        operations research and optimization. The critical path method; the
        knapsack problem, traveling salesman problem, introduction to set
        covering models Linear, combinatorial methods. Use of spreadsheet and
        other software tools. Duality, sensitivity analysis. Models for business
        operations and planning, computer systems, transportation, finance.
        Network models, and integer linear programming. DS413: Scheduling
        Techniques This course focuses on developing effective project
        schedules. Proven techniques are applied to each of the following: work
        breakdown structure creation, realistic estimate development, functional
        dependency definition, task constraint management, resource assignment,
        schedule optimization, baseline creation, and variance tracking.
        Extensive hands-on exercises using Microsoft project database for
        effectively process tasks, estimates, dependencies, constraints,
        deadlines, resources, and assignments. Optimize the schedule to meet
        deadlines and budget restrictions. Balance resource workloads through
        the application of advanced resource-driven scheduling techniques,
        create project state reports, manage baselines and update project
        actual, crash or fast-track a project schedule. DS414: Quality Control
        and Reliability. This course is an introduction to Principles of
        statistical quality control including control by variable and by
        attribute, construction and use of control charts for variables,
        fraction defectives and number of defects and use of standard plans,
        reliability and life cycle testing. DS415: Stochastic Processes and
        Queuing Models. This course is an introduction to stochastic dynamic
        systems, queuing networks, probabilistic state transition models and
        nondeterministic decision-making models DS416: Data Analytics
        Programming Data-driven analysis has wrought a quiet revolution in
        business. As disk storage and computing power have become cheaper,
        companies have started maintaining detailed logs of inventories, sales,
        and customer activity, among others. Yet, this is only half the job; the
        real need is for ةعماج قيزاقزلا– لك ةي تابساحلا تامولعملاو Zagazig
        University – Faculty of Computers and Informatics
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــ ـــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ةحئلالا ةيلخادلا ل لا ةلحرم سويرولاكب – ماظنب تاعاسلا دمتعملا ة ةحفص 73
        نم 74 insights, and this course teaches you the tools for that. We will
        learn data analysis in Python, a general-purpose language that lies at
        the intersection of (a) easy enough to learn, (b) fast enough to scale,
        and (c) endowed with a wide range of powerful libraries that make data
        cleaning, visualization, and many common data analysis tasks a cinch.
        DS417: Decision Analysis This course offers practical guidance about how
        to make better decisions and teaches students how to use modeling to do
        decision analysis. We analyze decisions involving uncertainty, risk, and
        time delay. In addition to methods of decision analysis, the course will
        also emphasize sensitivity analysis and communication of
        recommendations. Prior and posterior distributions; conjugate priors;
        value of information; applications to decision making in business DS418:
        Network Modelling and Optimization This course provides an overview on
        network optimization fundamentals and engineering applications with
        focus on linear, nonlinear, and discrete problems. The interplay between
        discrete and continuous problem structures will be highlighted. Discrete
        network optimization problems will be discussed in the detail. In the
        case of continuous network optimization, duality and iterative cost
        improvement will be studied and applied in most common linear cost
        problems, such as minimum cost flow and transshipment problems. Main
        solution methods, including branch-and-bound, Lagrangian relaxation,
        Dantzig-Wolf decomposition, heuristics, and local search methods will be
        studied. DS419: Advanced Project Management This course includes project
        management body of knowledge (PMBOK) and project management systems,
        pricing and estimating, project risk management, managing multiple
        projects and enterprise project management, communication skills, effect
        of concurrent engineering, critical chain project management, dependency
        structure matrix, object-oriented project management. DS425: Selected
        Topics in Operations Research and Decision Support I Selected Topics
        provides an opportunity to study a topic which is not included in the
        existing curriculum. This course examines one or more selected current
        issues in the area of Operations Research and Decision Support. Topics
        chosen for study will be by arrangement with the department. DS426:
        Selected Topics in Operations Research and Decision Support II Selected
        Topics provides an opportunity to study a topic which is not included in
        the existing curriculum. This course examines one or more selected
        current issues in the area of Operations Research and Decision Support.
        Topics chosen for study will be by arrangement with the department.
        تامولعملاو تابساحلا لك قيزاقزلا ةي ةعماج Zagazig University – Faculty of
        Computers and Informatics ـــــــ ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        DS430/DS431: Project ــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        This course will continue for two semesters. In the first semester, a
        group of students will select one of the projects proposed by the
        department and analyze the underlying problem. In the second semester,
        the design and implementation of the project will be conducted. The
        student will deliver oral presentations, progress reports, and a final
        report.
        ــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        نم 74 ةحفص
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        ة دمتعملا
        ـــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــــ
        تاعاسلا ماظنب سويرولاكب لا ةلحرم ل ةيلخادلا ةحئلالا 74
      </p>
    </>
  );
};

export default CollegeGuide;
